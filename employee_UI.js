// UI module for handling DOM manipulation and UI updates

const ITEMS_PER_PAGE = 12;
let currentPage = 1;

/**
 * @description 獲取頁面上的 DOM 元素
 * @returns {Object} DOM 元素物件
 */
function getDOMElements() {
    return {
        cardsContainer: document.getElementById('employee-cards-container'),
        paginationControls: document.getElementById('pagination-controls'),
        modal: document.getElementById('employeeModal'),
        modalTitle: document.getElementById('employeeModalTitle'),
        form: document.querySelector('#employeeModal form'),
        departmentSelect: document.getElementById('department'),
        searchDepartmentSelect: document.getElementById('searchDepartment'),
        positionSelect: document.getElementById('position'),
        searchNameInput: document.querySelector('input[name="searchName"]'),
        searchIdInput: document.querySelector('input[name="searchId"]')
    };
}

/**
 * @description 渲染員工卡片列表
 * @param {Array} employees - 要顯示的員工資料陣列
 * @param {Map} departmentsMap - 部門資料 Map
 * @param {Map} positionsMap - 職位資料 Map
 */
function renderEmployeeCards(employees, departmentsMap, positionsMap) {
    const { cardsContainer } = getDOMElements();
    cardsContainer.innerHTML = '';

    if (employees.length === 0) {
        displayNoEmployeesMessage();
        return;
    }

    const paginatedEmployees = paginate(employees, currentPage, ITEMS_PER_PAGE);

    paginatedEmployees.forEach(emp => {
        const department = departmentsMap.get(emp.departmentId);
        const position = positionsMap.get(emp.positionId);
        const card = document.createElement('div');
        card.className = 'bg-white rounded-xl shadow-lg p-6 flex flex-col justify-between transform hover:-translate-y-1 transition-transform duration-300';

        const statusColor = emp.status === '在職' ? 'text-green-500' : 'text-red-500';
        const nameInitial = emp.name ? emp.name.charAt(0) : '？';

        card.innerHTML = `
            <div class="flex-grow">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 rounded-full bg-blue-100 flex items-center justify-center">
                            <span class="text-2xl font-bold text-blue-500">${nameInitial}</span>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex justify-between items-center">
                            <p class="text-lg font-bold text-gray-900 truncate">${emp.name}</p>
                            <p class="text-sm font-medium ${statusColor}">${emp.status}</p>
                        </div>
                        <p class="text-sm text-gray-500 truncate">${position ? position.name : '未知職位'}</p>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200 space-y-3 text-sm">
                    <div class="flex items-center text-gray-600">
                        <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 4h5m-5 4h5"></path></svg>
                        <span>${department ? department.name : '未知部門'}</span>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg>
                        <span>${emp.empNo}</span>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-2 mt-6">
                <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-sm flex items-center" onclick="handleEditEmployee('${emp.id}')">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z"></path></svg>
                    編輯
                </button>
                <button class="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm flex items-center" onclick="handleDeleteEmployee('${emp.id}')">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                    刪除
                </button>
            </div>
        `;
        cardsContainer.appendChild(card);
    });

    renderPaginationControls(employees.length);
}

/**
 * @description 顯示無員工資料的訊息
 */
function displayNoEmployeesMessage() {
    const { cardsContainer } = getDOMElements();
    cardsContainer.innerHTML = `
        <div class="col-span-full text-center py-12">
            <span class="fa fa-exclamation-triangle fa-3x text-gray-400"></span>
            <p class="mt-4 text-xl text-gray-600">目前無任何員工資料。</p>
        </div>
    `;
}

/**
 * @description 渲染分頁控制項
 * @param {number} totalItems - 項目總數
 */
function renderPaginationControls(totalItems) {
    const { paginationControls } = getDOMElements();
    paginationControls.innerHTML = '';
    const pageCount = Math.ceil(totalItems / ITEMS_PER_PAGE);

    if (pageCount <= 1) return;

    for (let i = 1; i <= pageCount; i++) {
        const button = document.createElement('button');
        button.textContent = i;
        button.className = `px-4 py-2 rounded ` + (i === currentPage ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-200');
        button.onclick = () => {
            currentPage = i;
            // This assumes a global function `handlePageChange` exists in the controller
            handlePageChange();
        };
        paginationControls.appendChild(button);
    }
}

/**
 * @description 對資料進行分頁
 * @param {Array} items - 所有項目
 * @param {number} page - 當前頁碼
 * @param {number} perPage - 每頁項目數
 * @returns {Array} 當前頁的項目
 */
function paginate(items, page, perPage) {
    const start = (page - 1) * perPage;
    const end = start + perPage;
    return items.slice(start, end);
}

/**
 * @description 填充部門和職位的下拉選單
 */
function populateDropdowns(departments, positions) {
    const { departmentSelect, searchDepartmentSelect, positionSelect } = getDOMElements();

    departmentSelect.innerHTML = '<option value="">請選擇部門</option>';
    departments.forEach(dept => {
        departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
    });

    searchDepartmentSelect.innerHTML = '<option value="全部">全部</option>';
    departments.forEach(dept => {
        searchDepartmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
    });

    positionSelect.innerHTML = '<option value="">請選擇職位</option>';
    positions.forEach(pos => {
        positionSelect.innerHTML += `<option value="${pos.id}">${pos.name}</option>`;
    });
}

/**
 * @description 顯示模態框
 */
function showModal(title) {
    const { modal, modalTitle, form } = getDOMElements();
    modal.classList.remove('hidden');
    modalTitle.textContent = title;
    form.reset();
}

/**
 * @description 隱藏模態框
 */
function hideModal() {
    const { modal } = getDOMElements();
    modal.classList.add('hidden');
}

/**
 * @description 將員工資料填充到編輯表單中
 * @param {Object} employee - 員工資料物件
 */
function fillFormForEdit(employee) {
    const { form } = getDOMElements();
    form.elements['empNo'].value = employee.empNo || '';
    form.elements['name'].value = employee.name || '';
    form.elements['department'].value = employee.departmentId || '';
    form.elements['position'].value = employee.positionId || '';
    form.elements['phone'].value = employee.phone || '';
    form.elements['email'].value = employee.email || '';
    form.elements['address'].value = employee.address || '';
    form.elements['idNumber'].value = employee.idNumber || '';
    form.elements['salary'].value = employee.salary || 0;
    form.elements['supervisorAllowance'].value = employee.supervisorAllowance || 0;
    form.elements['mealAllowance'].value = employee.mealAllowance || 0;
    form.elements['companyLaborInsurance'].value = employee.companyLaborInsurance || 0;
    form.elements['companyHealthInsurance'].value = employee.companyHealthInsurance || 0;
    form.elements['companyRetirementInsurance'].value = employee.companyRetirementInsurance || 0;
    form.elements['employeeLaborInsurance'].value = employee.employeeLaborInsurance || 0;
    form.elements['employeeHealthInsurance'].value = employee.employeeHealthInsurance || 0;
    form.elements['employeeRetirementInsurance'].value = employee.employeeRetirementInsurance || 0;
    form.elements['note'].value = employee.note || '';

    if (employee.startDate) {
        form.elements['startDate'].value = employee.startDate.replace(/\//g, '-');
    }
    if (employee.resignationDate) {
        form.elements['resignationDate'].value = employee.resignationDate.replace(/\//g, '-');
    }
}

/**
 * @description 從表單中獲取資料
 * @returns {Object} 表單資料物件
 */
function getFormData() {
    const { form } = getDOMElements();
    return {
        empNo: form.elements['empNo'].value,
        name: form.elements['name'].value,
        departmentId: form.elements['department'].value,
        positionId: form.elements['position'].value,
        phone: form.elements['phone'].value,
        email: form.elements['email'].value,
        address: form.elements['address'].value,
        salary: parseInt(form.elements['salary'].value) || 0,
        supervisorAllowance: parseInt(form.elements['supervisorAllowance'].value) || 0,
        mealAllowance: parseInt(form.elements['mealAllowance'].value) || 0,
        companyLaborInsurance: parseInt(form.elements['companyLaborInsurance'].value) || 0,
        companyHealthInsurance: parseInt(form.elements['companyHealthInsurance'].value) || 0,
        companyRetirementInsurance: parseInt(form.elements['companyRetirementInsurance'].value) || 0,
        employeeLaborInsurance: parseInt(form.elements['employeeLaborInsurance'].value) || 0,
        employeeHealthInsurance: parseInt(form.elements['employeeHealthInsurance'].value) || 0,
        employeeRetirementInsurance: parseInt(form.elements['employeeRetirementInsurance'].value) || 0,
        startDate: form.elements['startDate'].value.replace(/-/g, '/'),
        resignationDate: form.elements['resignationDate'].value ? form.elements['resignationDate'].value.replace(/-/g, '/') : '',
        idNumber: form.elements['idNumber'].value,
        note: form.elements['note'].value,
    };
}

/**
 * @description 顯示成功訊息
 */
function showSuccessMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded shadow-lg';
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);
    setTimeout(() => messageDiv.remove(), 3000);
}

/**
 * @description 獲取搜尋條件
 */
function getSearchCriteria() {
    const { searchNameInput, searchIdInput,searchDepartmentSelect } = getDOMElements();
    return {
        searchName: searchNameInput.value,
        searchDepartment: searchDepartmentSelect.value,
        searchId: searchIdInput.value
    };
}