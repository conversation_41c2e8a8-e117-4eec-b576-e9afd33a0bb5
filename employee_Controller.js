// Controller for employee page - orchestrates UI, Logic, and Service layers

let allEmployees = [];
let filteredEmployees = [];
let departmentsMap = new Map();
let positionsMap = new Map();
let currentEditId = null;
let debounceTimer;

document.addEventListener('DOMContentLoaded', async () => {
    await initializePage();
    setupEventListeners();
});

/**
 * @description 初始化頁面，獲取所有需要的資料並渲染初始視圖
 */
async function initializePage() {
    try {
        const [employees, departments, positions] = await Promise.all([
            getAllEmployees(),
            getAllDepartments(),
            getAllPositions()
        ]);

        allEmployees = employees;
        filteredEmployees = allEmployees;
        departmentsMap = new Map(departments.map(dept => [dept.id, dept]));
        positionsMap = new Map(positions.map(pos => [pos.id, pos]));

        populateDropdowns(departments, positions);
        renderEmployeeCards(filteredEmployees, departmentsMap, positionsMap);
    } catch (error) {
        console.error('頁面初始化失敗：', error);
        alert('頁面初始化失敗，請重新整理');
    }
}

/**
 * @description 設定頁面元素的事件監聽器
 */
function setupEventListeners() {
    const { searchNameInput,searchIdInput,searchDepartmentSelect } = getDOMElements();

    searchNameInput.addEventListener('keyup', () => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(handleSearch, 300);
    });
    searchIdInput.addEventListener('keyup', () => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(handleSearch, 300);
    });

    searchDepartmentSelect.addEventListener('change', handleSearch);

    const addEmployeeButton = document.querySelector('button.bg-green-500');
    addEmployeeButton.addEventListener('click', handleShowAddModal);

    const modalForm = document.querySelector('#employeeModal form');
    modalForm.addEventListener('submit', handleSaveEmployee);

    const cancelModalButton = document.querySelector('#employeeModal button.bg-gray-300');
    cancelModalButton.addEventListener('click', hideModal);
}

/**
 * @description 處理搜尋邏輯
 */
function handleSearch() {
    const searchCriteria = getSearchCriteria();
    filteredEmployees = filterEmployees(allEmployees, searchCriteria);
    currentPage = 1; // Reset to first page after search
    renderEmployeeCards(filteredEmployees, departmentsMap, positionsMap);
}

/**
 * @description 處理分頁變更
 */
function handlePageChange() {
    renderEmployeeCards(filteredEmployees, departmentsMap, positionsMap);
}

/**
 * @description 處理顯示新增員工模態框的事件
 */
function handleShowAddModal() {
    currentEditId = null;
    showModal('新增員工');
}

/**
 * @description 處理編輯員工按鈕點擊事件
 * @param {string} employeeId - 員工 ID
 */
async function handleEditEmployee(employeeId) {
    currentEditId = employeeId;
    const employee = allEmployees.find(emp => emp.id === employeeId);
    if (employee) {
        showModal('編輯員工資訊');
        fillFormForEdit(employee);
    }
}

/**
 * @description 處理儲存員工資料的表單提交事件
 * @param {Event} event - 表單提交事件
 */
async function handleSaveEmployee(event) {
    event.preventDefault();
    let formData = getFormData();
    const validationError = validateEmployeeForm(formData);

    if (validationError) {
        alert(validationError);
        return;
    }

    formData = processEmployeeData(formData);
    if (currentEditId) {
        formData.id = currentEditId;
    }

    try {
        await saveEmployee(formData);
        hideModal();
        await refreshEmployeeList();
        showSuccessMessage(currentEditId ? '員工資料已更新' : '新增員工成功');
    } catch (error) {
        console.error('儲存員工資料失敗：', error);
        alert('儲存員工資料失敗');
    }
}

/**
 * @description 處理刪除員工按鈕點擊事件
 * @param {string} employeeId - 員工 ID
 */
async function handleDeleteEmployee(employeeId) {
    if (!confirm('確定要刪除此員工資料嗎？')) return;

    try {
        await deleteEmployeeById(employeeId);
        await refreshEmployeeList();
        showSuccessMessage('員工資料已刪除');
    } catch (error) {
        console.error('刪除員工失敗：', error);
        alert('刪除員工失敗');
    }
}

/**
 * @description 重新整理員工列表
 */
async function refreshEmployeeList() {
    allEmployees = await getAllEmployees();
    handleSearch(); // Re-apply search and render
}

// Make functions available in the global scope
window.handleEditEmployee = handleEditEmployee;
window.handleDeleteEmployee = handleDeleteEmployee;
window.handlePageChange = handlePageChange;