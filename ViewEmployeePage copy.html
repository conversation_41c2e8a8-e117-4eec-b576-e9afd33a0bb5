<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查看員工詳細資料 - 益芯能源工程-管理系統</title>
    <link rel="icon" href="../../common/img/logo.png" type="image/x-icon">
    <script type="importmap">
    {
        "imports": {
        "@common/": "/common/"
        }
    }
    </script>
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-analytics-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-auth-compat.js"></script>
    <script src="../../common/firebaseAPI/auth.js"></script>
    <script src="../../common/db/db.js"></script>
    <script src="../../common/db/preload.js"></script>
    <script src="../../common/utils/CommonUtils.js"></script>
    <script src="../../common/utils/pageTransfer.js"></script>
    <script src="../../common/utils/ModalUtils.js"></script>
    <style>
        .nav-item:hover>.submenu {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            top: 100%;
            left: 0;
        }

        .submenu .submenu {
            top: 0;
            left: 100%;
        }

        .nav-item {
            position: relative;
        }
    </style>
    <script src="../../common/navigation/navigation.js"></script>
</head>

<body class="flex flex-col min-h-screen bg-gray-100">

    <main class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">員工資料管理</h1>
            <p class="text-gray-600">管理員工基本資料與薪資設定</p>
        </div>

        <!-- Page Header -->
        <div class="flex flex-wrap justify-between items-center gap-4 mb-8">
            <h1 class="text-3xl font-bold text-gray-800">員工詳細資料</h1>
            <div class="flex items-center gap-2">
                <button onclick="history.back()" class="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>返回
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg shadow-sm hover:bg-blue-700">
                    <i class="fas fa-edit mr-2"></i>編輯
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Left Column: 員工基本資料區 -->
            <div class="lg:col-span-3">

                <!-- 員工辨識區域 -->
                <div class="bg-white rounded-xl shadow-md p-6 text-center">
                    <div class="border-b border-gray-200 m-4 p-2">
                        <div class="w-32 h-32 rounded-full mx-auto bg-blue-100 flex items-center justify-center"><!-- Avatar Circle class="w-32 h-32 rounded-full mx-auto mb-4 border-4 border-gray-200"-->
                            <span class="text-4xl font-bold text-blue-500">林</span>
                        </div>
                        <h2 id="name_avatar" class="text-2xl font-bold text-gray-900"></h2>
                        <p id="empNo_avatar" class="text-sm text-gray-500"></p>
                    </div>
                    <div class="flex flex-row justify-between items-center text-center m-2">
                        <div class="flex flex-col justify-start items-start text-left m-2">
                            <div class="flex flex-row justify-start items-center text-center m-2">
                                <!-- 部門icon -->
                                <svg class="w-12 h-12" fill="#3c63d7" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-93.18 -93.18 652.25 652.25" xml:space="preserve" transform="rotate(0)" stroke="#3c63d7" stroke-width="9.31776"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="7.4542079999999995"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M464.283,357.994l-37.104-83.588c-1.698-3.826-4.679-6.997-8.392-8.93L361.201,235.5c-1.27-0.662-2.808-0.533-3.951,0.33 c-6.347,4.801-13.132,8.709-20.226,11.703l34.318,17.864c7.806,4.063,14.068,10.729,17.637,18.771l41.229,92.879 c1.017,2.289,1.8,4.643,2.354,7.026h14.762c6.305,0,12.119-3.154,15.555-8.439C466.316,370.349,466.842,363.755,464.283,357.994z"></path> <path d="M94.545,265.398l34.319-17.864c-7.094-2.996-13.88-6.901-20.228-11.703c-1.144-0.864-2.682-0.991-3.951-0.331 l-57.585,29.978c-3.713,1.933-6.692,5.103-8.39,8.929L1.604,357.994c-2.558,5.762-2.033,12.356,1.402,17.641 c3.436,5.285,9.251,8.439,15.555,8.439h14.763c0.556-2.386,1.339-4.737,2.355-7.028l41.23-92.878 C80.479,276.126,86.742,269.46,94.545,265.398z"></path> <path d="M160.519,231.186c1.367,0,2.305-0.369,2.953-0.964c-0.772-0.849-1.538-1.707-2.29-2.587 c-18.277-21.393-28.343-49.654-28.343-79.578c0-39.178,9.87-68.89,29.334-88.312c0.215-0.214,0.443-0.411,0.659-0.622 c-40.074,0.393-72.364,20.79-72.364,86.077C90.467,191.37,121.522,229.048,160.519,231.186z"></path> <path d="M333.05,148.057c0,29.924-10.066,58.186-28.344,79.578c-0.752,0.88-1.519,1.739-2.291,2.588 c0.601,0.734,1.787,0.963,2.952,0.963c38.995-2.136,70.052-39.815,70.052-85.985c0-65.288-32.291-85.685-72.363-86.077 c0.217,0.211,0.443,0.408,0.658,0.622C323.18,79.168,333.05,108.88,333.05,148.057z"></path> <path d="M232.945,243.712c45.134,0,81.724-42.827,81.724-95.654c0-73.26-36.589-95.654-81.724-95.654 c-45.137,0-81.726,22.395-81.726,95.654C151.219,200.885,187.81,243.712,232.945,243.712z"></path> <path d="M372.179,291.625c-1.887-4.252-5.198-7.774-9.323-9.923l-63.986-33.308c-1.411-0.735-3.12-0.592-4.391,0.367 c-18.097,13.689-39.375,20.926-61.533,20.926c-22.16,0-43.438-7.235-61.537-20.926c-1.271-0.961-2.979-1.104-4.391-0.367 l-63.985,33.308c-4.126,2.147-7.436,5.671-9.322,9.923l-41.23,92.88c-2.843,6.401-2.26,13.729,1.558,19.602 c3.818,5.872,10.279,9.378,17.284,9.378h323.247c7.004,0,13.466-3.506,17.282-9.378s4.399-13.2,1.56-19.602L372.179,291.625z"></path> </g> </g> </g></svg>

                                <div class="flex flex-col justify-start items-start m-2">
                                    <h2 class="text-sm font-bold text-gray-500">部門</h2>
                                    <p id="department_avatar" class="text-md font-bold text-blue-500">人資部門</p>
                                </div>
                            </div>
                            <div class="flex flex-row justify-start items-center text-center m-2">
                                <!-- 職位icon -->
                                <svg class="w-12 h-12" fill="#4685c8" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-78.11 -78.11 546.75 546.75" xml:space="preserve" stroke="#4685c8" stroke-width="7.8105400000000005"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M370.469,113.479c-8.584,0-15.959-0.054-33.705-0.079V45.162c0-13.974-8.047-19.543-15.801-19.597L15.045,25.561 C6.736,25.561,0,32.296,0,40.604V301.58c0,34.979,28.457,63.387,63.438,63.387H327.09c34.98,0,63.438-28.408,63.438-63.387 V133.538C390.527,122.461,381.547,113.479,370.469,113.479z M295.492,323.753H63.438c-12.254,0-22.223-9.947-22.223-22.173V66.776 l254.335,0.004L295.492,323.753z"></path> <path d="M70.09,148.56h195.152c3.748,0,6.787-3.04,6.787-6.789v-13.576c0-3.749-3.039-6.789-6.787-6.789H70.09 c-3.75,0-6.788,3.04-6.788,6.789v13.576C63.302,145.521,66.34,148.56,70.09,148.56z"></path> <path d="M137.814,174.556H70.475c-3.122,0-5.652,2.53-5.652,5.652v83.262c0,3.121,2.53,5.651,5.652,5.651h67.339 c3.122,0,5.653-2.53,5.653-5.651v-83.262C143.467,177.086,140.936,174.556,137.814,174.556z"></path> <path d="M265.242,182.449h-91.649c-3.748,0-6.787,3.039-6.787,6.788v9.603c0,3.749,3.039,6.788,6.787,6.788h91.649 c3.748,0,6.787-3.039,6.787-6.788v-9.603C272.029,185.488,268.99,182.449,265.242,182.449z"></path> <path d="M265.242,240.486h-91.649c-3.748,0-6.787,3.038-6.787,6.787v9.604c0,3.75,3.039,6.789,6.787,6.789h91.649 c3.748,0,6.787-3.039,6.787-6.789v-9.604C272.029,243.523,268.99,240.486,265.242,240.486z"></path> </g> </g> </g></svg>

                                <div class="flex flex-col justify-start items-start m-2">
                                    <h2 class="text-sm font-bold text-gray-500">職位</h2>
                                    <p id="position_avatar" class="text-md font-bold text-blue-500">人資人員</p>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-row justify-start items-center text-center m-2">
                            <!-- 到職日期icon -->
                            <svg class="w-12 h-12" fill="#68bb9f" viewBox="-4.8 -4.8 33.60 33.60" xmlns="http://www.w3.org/2000/svg" stroke="#68bb9f"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="m19.5 0h-3v3h3zm3 0h-1.5v4.5h-6v-4.5h-6v4.5h-6v-4.5h-1.5-.001c-.828 0-1.499.671-1.499 1.499v.001 21 .001c0 .828.671 1.499 1.499 1.499h.001 21 .001c.828 0 1.499-.671 1.499-1.499v-.001-21-.001c0-.828-.671-1.499-1.499-1.499zm-1.5 21h-18v-13.5h18zm-13.5-21h-3v3h3zm6 10.5h-3v3h3zm4.5 0h-3v3h3zm-9 4.5h-3v3h3zm0-4.5h-3v3h3zm4.5 4.5h-3v3h3zm4.5 0h-3v3h3z"></path></g></svg>

                            <div class="flex flex-col justify-start items-start m-2">
                                <h2 class="text-sm font-bold text-gray-500">到職日期</h2>
                                <p id="startDate_avatar" class="text-md font-bold text-blue-500">2025/06/15</p>
                            </div>
                        </div>
                    </div>
                    
                </div>

                <!-- 紀錄區域 -->
                <div class="bg-white rounded-xl shadow-md mt-4">
                    <!-- Tab Headers -->
                    <div class="border-b border-gray-200">
                        <nav id="tab-nav" class="-mb-px flex space-x-6 px-6" aria-label="Tabs">
                            <button data-tab="basic-info" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm text-blue-600 border-blue-600">
                                基本資料
                            </button>
                            <button data-tab="salary-info" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 border-transparent">
                                薪資紀錄
                            </button>
                             <button data-tab="attendance-info" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 border-transparent">
                                出勤紀錄
                            </button>
                             <button data-tab="contract-info" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 border-transparent">
                                合約/文件
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div id="tab-content" class="p-6">
                        <!-- Basic Info Tab -->
                        <div id="basic-info" class="tab-panel">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
                                <div class="flex items-center py-3">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" /></svg>
                                    <div><p class="text-sm text-gray-500">員工編號</p><p id="empNo" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-center py-3">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" /></svg>
                                    <div><p class="text-sm text-gray-500">姓名</p><p id="name" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-center py-3">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15A2.25 2.25 0 0 0 2.25 6.75v10.5A2.25 2.25 0 0 0 4.5 19.5Z" /></svg>
                                    <div><p class="text-sm text-gray-500">身分證號</p><p id="idNumber" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-center py-3">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21" /></svg>
                                    <div><p class="text-sm text-gray-500">部門</p><p id="department" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-center py-3">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.07a2.25 2.25 0 0 1-2.25 2.25H5.92a2.25 2.25 0 0 1-2.17-2.01L3.32 5.32a2.25 2.25 0 0 1 2.25-2.25h1.5a2.25 2.25 0 0 1 2.25 2.25v.15m3.85-2.06a2.25 2.25 0 0 0-2.25-2.25H9.75a2.25 2.25 0 0 0-2.25 2.25v.15m3.85-2.06a2.25 2.25 0 0 0 2.25-2.25h1.5a2.25 2.25 0 0 0 2.25 2.25v.15m0 0v-2.06a2.25 2.25 0 0 0-2.25-2.25H9.75a2.25 2.25 0 0 0-2.25 2.25v.15" /></svg>
                                    <div><p class="text-sm text-gray-500">職位</p><p id="position" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-center py-3">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z" /></svg>
                                    <div><p class="text-sm text-gray-500">電話</p><p id="phone" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-center py-3 md:col-span-2">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" /></svg>
                                    <div><p class="text-sm text-gray-500">電子郵件</p><p id="email" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-center py-3">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0h18M-4.5 12h22.5" /></svg>
                                    <div><p class="text-sm text-gray-500">到職日期</p><p id="startDate" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-center py-3">
                                    <svg class="w-6 h-6 text-gray-400 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0h18M-4.5 12h22.5" /></svg>
                                    <div><p class="text-sm text-gray-500">離職日期</p><p id="resignationDate" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-start py-3 md:col-span-2">
                                    <svg class="w-6 h-6 text-gray-400 mr-4 mt-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" /><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" /></svg>
                                    <div><p class="text-sm text-gray-500">地址</p><p id="address" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                                <div class="flex items-start py-3 md:col-span-2">
                                    <svg class="w-6 h-6 text-gray-400 mr-4 mt-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" /></svg>
                                    <div><p class="text-sm text-gray-500">備註</p><p id="note" class="text-base font-medium text-gray-900"></p></div>
                                </div>
                            </div>
                        </div>
                        <!-- Salary Info Tab -->
                        <div id="salary-info" class="tab-panel hidden">
                             <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-5">
                                <div class="py-2"><p class="text-sm text-gray-500">基本薪資</p><p id="salary" class="text-base font-medium text-gray-900"></p></div>
                                <div class="py-2"><p class="text-sm text-gray-500">主管津貼</p><p id="supervisorAllowance" class="text-base font-medium text-gray-900"></p></div>
                                <div class="py-2"><p class="text-sm text-gray-500">餐食津貼</p><p id="mealAllowance" class="text-base font-medium text-gray-900"></p></div>
                                <div class="md:col-span-2"><hr class="my-3"></div>
                                <div class="md:col-span-2">
                                    <p class="text-lg font-semibold text-gray-800 mb-2">勞健保公司負擔</p>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6">
                                        <div class="py-2"><p class="text-sm text-gray-500">勞工保險</p><p id="companyLaborInsurance" class="text-base font-medium text-gray-900"></p></div>
                                        <div class="py-2"><p class="text-sm text-gray-500">健康保險</p><p id="companyHealthInsurance" class="text-base font-medium text-gray-900"></p></div>
                                        <div class="py-2 md:col-span-2"><p class="text-sm text-gray-500">公司提撥6%勞工退休金</p><p id="companyRetirementInsurance" class="text-base font-medium text-gray-900"></p></div>
                                    </div>
                                </div>
                                <div class="md:col-span-2"><hr class="my-3"></div>
                                <div class="md:col-span-2">
                                    <p class="text-lg font-semibold text-gray-800 mb-2">勞健保員工自負</p>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6">
                                        <div class="py-2"><p class="text-sm text-gray-500">勞工保險</p><p id="employeeLaborInsurance" class="text-base font-medium text-gray-900"></p></div>
                                        <div class="py-2"><p class="text-sm text-gray-500">健康保險</p><p id="employeeHealthInsurance" class="text-base font-medium text-gray-900"></p></div>
                                        <div class="py-2 md:col-span-2"><p class="text-sm text-gray-500">自願提撥勞工退休金</p><p id="employeeRetirementInsurance" class="text-base font-medium text-gray-900"></p></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Attendance Info Tab -->
                        <div id="attendance-info" class="tab-panel hidden">
                            <p>出勤紀錄內容...</p>
                        </div>
                        <!-- Contract Info Tab -->
                        <div id="contract-info" class="tab-panel hidden">
                            <p>合約/文件內容...</p>
                        </div>
                    </div>
                </div>
                
            </div>

            <!-- Right Column: 搜尋其他員工區域 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-md p-6 text-center">
                    <div class="border-b border-gray-200 m-4 p-2">
                        <!--搜尋框-->
                        <div class="relative pt-2 mx-auto text-gray-600">
                            <input class="border-2 border-gray-300 bg-white h-10 px-5 pr-16 rounded-lg text-sm focus:outline-none w-full"
                            type="search" name="search" placeholder="Search">
                            <button type="submit" class="absolute right-0 top-0 mt-5 mr-4">
                            <svg class="text-gray-600 h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Capa_1" x="0px" y="0px"
                                viewBox="0 0 56.966 56.966" style="enable-background:new 0 0 56.966 56.966;" xml:space="preserve"
                                width="512px" height="512px">
                                <path
                                d="M55.146,51.887L41.588,37.786c3.486-4.144,5.396-9.358,5.396-14.786c0-12.682-10.318-23-23-23s-23,10.318-23,23  s10.318,23,23,23c4.761,0,9.298-1.436,13.177-4.162l13.661,14.208c0.571,0.593,1.339,0.92,2.162,0.92  c0.779,0,1.518-0.297,2.079-0.837C56.255,54.982,56.293,53.08,55.146,51.887z M23.984,6c9.374,0,17,7.626,17,17s-7.626,17-17,17  s-17-7.626-17-17S14.61,6,23.984,6z" />
                            </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-4 flex flex-row justify-start items-center text-center m-2 hover:shadow-lg hover:border-2 hover:border-gray-200 hover:rounded-lg">
                        <!-- 頭像icon -->
                        <svg  class="w-12 h-12" fill="#3c63d7" version="1.1"viewBox="0 0 256 256" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <defs> <style>.cls-1{fill:#84d0f7;}.cls-2{fill:#aa392d;}.cls-3{fill:#7c211a;}.cls-4{fill:#eab198;}.cls-5{fill:#2d2d2d;}.cls-6{opacity:0.2;}.cls-7{opacity:0.3;}</style> </defs> <g data-name="Male 2" id="Male_2"> <path class="cls-1" d="M250.57,128.13a122,122,0,0,1-37.91,88.44H44.76a101.85,101.85,0,0,1-9-9l-.83-1c-7-8.35-29.75-38-28.57-78.51C7.94,72,55.14,6,128.44,6A122.13,122.13,0,0,1,250.57,128.13Z" id="Wallpaper"></path> <path class="cls-2" d="M222,206.68a122.41,122.41,0,0,1-93.54,43.58c-39.71,0-69.44-21.09-83.67-33.69a104.1,104.1,0,0,1-9-9c-.25-.28-.54-.6-.82-1C48.52,192.9,66.85,182.24,88,176.19c5.92,14.71,21.81,25.25,40.45,25.25,17.33,0,32.27-9.09,39-22.16.54-1,1-2,1.43-3.09l1.15.33C190.66,182.64,208.59,193.17,222,206.68Z" id="Sweater"></path> <path class="cls-3" d="M168.86,176.19c-.43,1.05-.89,2.07-1.43,3.09-6.75,13.07-21.69,22.16-39,22.16-18.64,0-34.53-10.54-40.45-25.25l2.67-.73.22-.07s1.78-.47,3.43-.84a37.95,37.95,0,0,0,68.27,0A28.13,28.13,0,0,1,168.86,176.19Z" id="Neckband"></path> <path class="cls-4" d="M162.55,174.55a37.94,37.94,0,0,1-68.27,0c1.35-.34,2.71-.63,4.08-.91l1-.2c.17-.05.35-.09.51-.12l.87-.17,1.2-.2c.28-.07.6-.11.91-.18H103c.35-.87.68-1.8,1-2.82.11-.34.22-.71.33-1.07a40.67,40.67,0,0,0,.89-4.07,39.88,39.88,0,0,0,6.91,4.6c.18.09.35.18.53.29l.62.31.76.36c.16.07.31.13.44.2l.38.18.47.2.44.17a.69.69,0,0,0,.2.09,4.75,4.75,0,0,0,.54.2l.66.27h0l.73.27.16,0,.51.18.78.22c.11,0,.22.07.35.09l.71.2a38.17,38.17,0,0,0,4.54.8l.46,0c.11,0,.22,0,.34,0l.62,0,.55,0c.2,0,.43,0,.65,0h.55a30.33,30.33,0,0,0,9.74-1.63.07.07,0,0,0,.06,0,1.7,1.7,0,0,0,.36-.11c.8-.27,1.51-.56,2.13-.8l.85-.36.22-.08a38.68,38.68,0,0,0,9.89-6.14c.31,1.38.69,2.85,1.16,4.38.42,1.38.86,2.67,1.31,3.89.37.07.73.11,1.11.2l1.09.2,1.09.2c.26,0,.53.11.8.15a4.54,4.54,0,0,0,.53.12C159.86,173.92,161.21,174.21,162.55,174.55Z" id="Neck"></path> <path class="cls-5" d="M79.85,94.5a67.36,67.36,0,0,0-1,15.29c0,1,.11,2.06.22,3.09,0,.37.06.77.11,1.15-2.11-1.11-3-6-4.65-15.31-1-5.87-1.55-8.8-1.4-12a37,37,0,0,1,4.09-14.23,17.8,17.8,0,0,0,6.25,9.34A69,69,0,0,0,79.85,94.5Z" id="Hairs"></path> <path class="cls-5" d="M182.24,98.72c-.8,4.53-1.43,8-2.09,10.51-.71,2.71-1.49,4.29-2.63,4.82.18-1.6.32-3.17.38-4.77a65,65,0,0,0-3.51-24.65s0-.07,0-.09a83.27,83.27,0,0,0,7.75-5.86,36.07,36.07,0,0,1,1.52,8v0C183.81,89.94,183.28,92.88,182.24,98.72Z" data-name="Hairs" id="Hairs-2"></path> <path class="cls-6" d="M170,176.52c-.82,1-1.69,1.87-2.58,2.76a57.05,57.05,0,0,1-6.28,5.47,44.46,44.46,0,0,1-9.52,5.4A35.78,35.78,0,0,1,145,192.1a30.8,30.8,0,0,1-6.07.54,32.7,32.7,0,0,1-11.62-2.43,4.63,4.63,0,0,1-.64-.29,41.9,41.9,0,0,1-8.34-4.73,59.9,59.9,0,0,1-12.69-12.87c-.57-.77-1.13-1.55-1.68-2.37.11-.34.22-.71.33-1.07a40.67,40.67,0,0,0,.89-4.07c.58.47,1.18.94,1.8,1.38a45.8,45.8,0,0,0,5.11,3.22c.18.09.35.18.53.29l.62.31.76.36c.16.07.31.13.44.2l.38.18.47.2.44.17a.69.69,0,0,0,.2.09l.54.2.66.27h0l.73.27.16,0,.51.18.78.22c.11,0,.22.07.35.09l.71.2a31.48,31.48,0,0,0,4.54.8l.46,0c.11,0,.22,0,.34,0l.62,0,.55,0c.2,0,.43,0,.65,0h.55a31.93,31.93,0,0,0,9.74-1.63.07.07,0,0,0,.06,0,1.7,1.7,0,0,0,.36-.11c.67-.22,1.31-.47,2-.73l.2-.07c.26-.11.53-.22.82-.36l.22-.08a46.4,46.4,0,0,0,8.27-4.89c.55-.4,1.09-.83,1.62-1.25.31,1.38.69,2.85,1.16,4.38.42,1.38.86,2.67,1.31,3.89.37.07.73.11,1.11.2l1.09.2,1.09.2c.26,0,.53.11.8.15a4.54,4.54,0,0,0,.53.12c1.38.28,2.73.57,4.07.91,2.13.49,4.24,1,6.31,1.64Z" id="NeckShadow"></path> <path class="cls-4" d="M186.6,120.16c-.16,5.2-3.48,10.47-7.19,12.41a7.11,7.11,0,0,1-1.58.6l-.12,0a5.07,5.07,0,0,1-2.05.06,5,5,0,0,1-2-.88l-.28-.21a7.39,7.39,0,0,1-1.13-1.11c-2.68-3.23-3.77-9.29-2-14.13a14.78,14.78,0,0,1,3.23-5.06,9.08,9.08,0,0,1,4.44-2.6,5.91,5.91,0,0,1,2.37,0C184.7,110.15,186.74,115.81,186.6,120.16Z" id="RightEar"></path> <path class="cls-7" d="M186.6,120.16c-.16,5.2-3.48,10.47-7.2,12.41a7,7,0,0,1-1.57.6l-.12,0a5.07,5.07,0,0,1-2.05.06,5,5,0,0,1-2-.88l-.28-.21a7,7,0,0,1-1.13-1.11c-2.68-3.23-3.77-9.29-2-14.13a14.78,14.78,0,0,1,3.23-5.06,9.08,9.08,0,0,1,4.44-2.6,5.91,5.91,0,0,1,2.37,0C184.7,110.15,186.74,115.81,186.6,120.16Z" id="RightEarShadow"></path> <path class="cls-4" d="M84.57,131.06a7.28,7.28,0,0,1-1.41,1.32,5.13,5.13,0,0,1-2,.88,5.07,5.07,0,0,1-2.05-.06l-.12,0a7.11,7.11,0,0,1-1.58-.6c-3.71-1.94-7-7.21-7.2-12.41-.13-4.35,1.91-10,6.33-10.9,2.51-.51,4.91.71,6.8,2.61a14.66,14.66,0,0,1,3.24,5.06C88.34,121.77,87.24,127.83,84.57,131.06Z" id="LeftEar"></path> <path class="cls-4" d="M177.9,109.28c-.06,1.6-.2,3.17-.38,4.77a80.14,80.14,0,0,1-4.17,18.12c-4.67,13.11-12.52,24.73-22,32.33-.53.42-1.07.85-1.62,1.25a46.4,46.4,0,0,1-8.27,4.89l-.22.08c-.29.14-.56.25-.82.36l-.2.07c-.65.26-1.29.51-2,.73a1.7,1.7,0,0,1-.36.11.07.07,0,0,1-.06,0,31.93,31.93,0,0,1-9.74,1.63h-.55c-.22,0-.45,0-.65,0l-.55,0-.62,0c-.12,0-.23,0-.34,0l-.46,0a31.48,31.48,0,0,1-4.54-.8l-.71-.2c-.13,0-.24-.07-.35-.09l-.78-.22-.51-.18-.16,0-.73-.27h0l-.66-.27-.54-.2a.69.69,0,0,1-.2-.09l-.44-.17-.47-.2-.38-.18c-.13-.07-.28-.13-.44-.2l-.76-.36-.62-.31c-.18-.11-.35-.2-.53-.29a45.8,45.8,0,0,1-5.11-3.22c-.62-.44-1.22-.91-1.8-1.38a57.24,57.24,0,0,1-8.6-8.53,78.57,78.57,0,0,1-13.67-25.09A81.2,81.2,0,0,1,79.21,114c0-.38-.09-.78-.11-1.15-.11-1-.18-2-.22-3.09a67.36,67.36,0,0,1,1-15.29A69,69,0,0,1,83.5,81.79c.66-1.76,1.42-3.51,2.24-5.25a65.61,65.61,0,0,1,17.09-22.49c.31-.26.62-.51.94-.75a39.76,39.76,0,0,1,24.49-8.89C138,44.39,146.7,48.12,154,54a58.4,58.4,0,0,1,10.24,10.91,74.31,74.31,0,0,1,8.64,15.51c.54,1.38,1.07,2.71,1.54,4.09,0,0,0,.07,0,.09a65,65,0,0,1,3.51,24.65Z" id="Head"></path> <path class="cls-6" d="M183.64,86.68v0a26.09,26.09,0,0,1-3.43,3c-.09.09-.55.4-1.15.82-1,.65-2,1.29-3,1.91a74.33,74.33,0,0,1-13.42,6.43,76.87,76.87,0,0,1-62.09-4.47c-2.25-1.22-4.43-2.56-6.52-4-.71-.49-1.4-1-2.09-1.49a64.17,64.17,0,0,1-5.28-4.29c-.8-.71-1.6-1.44-2.36-2.2a40.61,40.61,0,0,0,8.78,5c6.46,2.85,14.18,5,21.67,7.07a73.55,73.55,0,0,0,19.93,3,65.77,65.77,0,0,0,20.07-3,56.6,56.6,0,0,0,8.58-3.57c1.31-.65,2.62-1.36,4-2.09,2.63-1.43,4.91-2.8,7.05-4.25a82.89,82.89,0,0,0,7.67-5.8A32.88,32.88,0,0,1,183.64,86.68Z" id="HairsShadow"></path> <path class="cls-5" d="M199.37,57c-.07,5.6-5.33,10.71-6.87,12.15-3.93,3.74-7.18,6.8-10.38,9.52l-.08.06a82.89,82.89,0,0,1-7.67,5.8c-2.14,1.45-4.42,2.82-7.05,4.25-1.35.73-2.66,1.44-4,2.09a56.6,56.6,0,0,1-8.58,3.57,65.77,65.77,0,0,1-20.07,3,73.55,73.55,0,0,1-19.93-3c-7.49-2.09-15.21-4.22-21.67-7.07a40.61,40.61,0,0,1-8.78-5l0,0c-.27-.22-.54-.42-.8-.64a17.8,17.8,0,0,1-6.25-9.34v0C74.88,63,81.79,53.56,82.83,52.16a30.41,30.41,0,0,1,12.49-9.42c9.36-4,13.69-.87,34,0,18.07.78,27.12,1.18,35.65-2.16,3.44-1.35,9.89-4.33,11-2.73.91,1.29-1.74,5.11-2.36,6-2.33,3.36-4.29,4.27-4,5.07.51,1.29,6.16.66,10.87-.71,5-1.47,6.89-3.2,7.78-2.36,1.2,1.13-.6,6-3.63,9.22-2.17,2.34-4.2,3-4,3.8.29,1.07,4.17,1.45,7.77.54,5.69-1.43,8.58-5.58,10.18-4.54C199.08,55.1,199.39,55.67,199.37,57Z" data-name="Hairs" id="Hairs-3"></path> </g> </g></svg>
                        <div class="flex flex-col justify-start items-start m-2">
                            <h2 class="text-xs font-bold text-gray-500">人資部門</h2>
                            <p class="text-md font-bold text-blue-500">王傳一</p>
                        </div>
                    </div>
                    <div class="p-4 flex flex-row justify-start items-center text-center m-2 hover:shadow-lg hover:border-2 hover:border-gray-200 hover:rounded-lg">
                        <!-- 頭像icon -->
                        <svg class="w-12 h-12" viewBox="0 0 256 256" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <defs> <style>.cls-1{fill:#84d0f7;}.cls-2{fill:#aa392d;}.cls-3{fill:#7c211a;}.cls-4{fill:#e59973;}.cls-5{fill:#2d2d2d;}.cls-6{opacity:0.2;}.cls-7{opacity:0.3;}</style> </defs> <g data-name="Male 3" id="Male_3"> <path class="cls-1" d="M250,128.15a121.08,121.08,0,0,1-28.47,78.18,123.13,123.13,0,0,1-9.26,9.85H45.15a101.45,101.45,0,0,1-9-8.91c-.24-.28-.52-.6-.82-1-7-8.31-29.62-37.8-28.45-78.15C8.49,72.26,55.48,6.59,128.44,6.59A121.55,121.55,0,0,1,250,128.15Z" id="Wallpaper"></path> <path class="cls-2" d="M221.54,206.34a130.68,130.68,0,0,1-9.27,9.84,121.2,121.2,0,0,1-83.83,33.54c-39.53,0-69.15-21-83.29-33.54a105.43,105.43,0,0,1-9-8.91c-.22-.29-.51-.6-.82-1C48.89,192.63,67.14,182,88.16,176c5.88,14.64,21.67,25.13,40.25,25.13,17.26,0,32.1-9.05,38.85-22.05.53-1,1-2,1.41-3.08l1.15.33C190.37,182.41,208.22,192.89,221.54,206.34Z" id="Sweater"></path> <path class="cls-3" d="M168.67,176c-.42,1-.88,2.06-1.41,3.08-6.75,13-21.59,22.05-38.85,22.05-18.58,0-34.37-10.49-40.25-25.13.44-.13.88-.24,1.32-.38.22,0,.42-.11.64-.17s.47-.11.69-.18l.22-.07s1.75-.46,3.41-.84a37.76,37.76,0,0,0,67.95,0A28.09,28.09,0,0,1,168.67,176Z" id="Neckband"></path> <path class="cls-4" d="M162.39,174.35a37.76,37.76,0,0,1-67.95,0c1.35-.33,2.7-.64,4.07-.9l1-.22c.17,0,.33-.07.5-.09l.87-.18,1.19-.2c.29-.07.6-.11.91-.18h.09c.33-.86.68-1.79,1-2.81.11-.33.22-.7.33-1.06a35,35,0,0,0,.87-4,41.53,41.53,0,0,0,6.9,4.57c.17.09.35.18.53.29s.42.2.62.31.48.22.75.36l.44.19c.11.07.25.11.38.18s.31.13.44.2.31.11.44.18h0l.19.09c.16.06.34.13.54.19s.42.18.66.27h0l.73.27.16,0c.15.07.33.11.51.18s.51.15.75.22.24.06.38.09l.68.2a36,36,0,0,0,4.54.79c.15,0,.31,0,.46.05l.33,0,.62,0,.55,0a4.29,4.29,0,0,1,.62,0h.56a30.48,30.48,0,0,0,9.71-1.62.07.07,0,0,0,.06,0,2.47,2.47,0,0,0,.36-.11c.79-.27,1.48-.55,2.12-.8l.84-.35c.07,0,.13-.07.22-.09a38.29,38.29,0,0,0,9.85-6.1c.31,1.37.68,2.83,1.15,4.35.42,1.37.86,2.66,1.3,3.87.38.07.73.11,1.11.2s.73.14,1.08.2l1.09.2.79.16c.18,0,.36.06.53.11C159.71,173.73,161.06,174,162.39,174.35Z" id="Neck"></path> <path class="cls-5" d="M197.43,82a7.84,7.84,0,0,1-.35,2.41c-1.6,5.62-7.08,6.19-8.1,11.1-.2.91.09.45,0,3.23-.07,2-.22,6.57-1.53,9.56a10.68,10.68,0,0,1-3.27,4,6.66,6.66,0,0,0-4.14-3,6,6,0,0,0-2.36,0,64.65,64.65,0,0,0-3.5-24.53c-.46-1.4-1-2.79-1.55-4.16A73.84,73.84,0,0,0,164,65.26c-8.61-11.75-20.91-20.49-35.77-20.44a39.66,39.66,0,0,0-24.38,8.85C96.27,59.59,90.17,67.93,85.94,76.8a72.17,72.17,0,0,0-5.86,17.88,67,67,0,0,0-1,14.71,5.63,5.63,0,0,0-2.35,0,7,7,0,0,0-4.6,3.7,10.16,10.16,0,0,1-3.32-3.7c-2.28-4.8,1.11-7.47-1.26-12.43-2.1-4.4-6.08-4.82-7.21-9.91a12.28,12.28,0,0,1-.18-3.78c.53-4.71,3.39-5,4.34-8.74,1.33-5.28-3.5-8.36-1.35-14.86a5.53,5.53,0,0,1,.55-1.37c2.75-4.78,10.27-1.24,14-5.11,3.47-3.56-.25-9.34,4.71-14.2a7.61,7.61,0,0,1,2.1-1.6c5.57-2.7,10.86,4.91,15.79,3,3.94-1.53,2.55-7.06,8.17-10.28a12,12,0,0,1,6.61-1.64c6.37.35,7.92,6.08,12.08,5.53,4-.55,3.78-5.93,9.18-7.66a11.21,11.21,0,0,1,9,1.07c5,3.07,3.52,8.6,7.48,10.08,3.18,1.22,4.93-2.14,11.08-1.79,2,.11,4.8.29,7.21,2,5.55,3.92,2.08,11.06,7.1,14.67,3.52,2.54,7.1.37,12.17,4.33a8.83,8.83,0,0,1,2.7,2.79c2.52,4.49-2.46,8-.82,13.14C193.58,76.63,197.43,77.49,197.43,82Z" id="Hairs"></path> <path class="cls-6" d="M169.82,176.32c-.84,1-1.68,1.86-2.56,2.75a57.77,57.77,0,0,1-6.26,5.44,44,44,0,0,1-9.47,5.37,36,36,0,0,1-6.59,2,29.81,29.81,0,0,1-6,.51A32,32,0,0,1,127.33,190c-.22-.09-.44-.18-.66-.29a41.37,41.37,0,0,1-8.28-4.71,59.34,59.34,0,0,1-12.63-12.81c-.57-.77-1.13-1.57-1.68-2.37.11-.33.22-.7.33-1.06a35,35,0,0,0,.87-4c.59.46,1.19.92,1.81,1.37a45,45,0,0,0,5.09,3.2c.17.09.35.18.53.29s.42.2.62.31.48.22.75.36l.44.19a4.11,4.11,0,0,0,.38.18c.15.07.31.13.44.2s.31.11.44.18h0l.19.09.54.19.66.27h0l.73.27.16,0c.15.07.33.11.51.18s.51.15.75.22.24.06.38.09l.68.2a32.19,32.19,0,0,0,4.54.79c.15,0,.31,0,.46.05l.33,0,.62,0,.55,0a4.29,4.29,0,0,1,.62,0h.56a32,32,0,0,0,9.71-1.62.07.07,0,0,0,.06,0,2.47,2.47,0,0,0,.36-.11c.66-.22,1.3-.47,1.94-.73a.61.61,0,0,0,.18-.07c.29-.11.55-.24.84-.35.07,0,.13-.07.22-.09a45,45,0,0,0,8.23-4.87c.55-.39,1.09-.81,1.62-1.23.31,1.37.68,2.83,1.15,4.35.42,1.37.86,2.66,1.3,3.87.38.07.73.11,1.11.2s.73.14,1.08.2l1.09.2.79.16c.18,0,.36.06.53.11,1.37.28,2.72.57,4,.9,2.12.49,4.23,1,6.28,1.64Z" id="NeckShadow"></path> <path class="cls-4" d="M186.33,120.22c-.15,5.18-3.46,10.42-7.16,12.35a7,7,0,0,1-1.57.6l-.12,0a5.2,5.2,0,0,1-2,.06,5,5,0,0,1-2-.88,1.86,1.86,0,0,1-.28-.21,6.84,6.84,0,0,1-1.12-1.1c-2.67-3.22-3.76-9.25-2-14.06a14.61,14.61,0,0,1,3.23-5,8.82,8.82,0,0,1,4.41-2.59,5.86,5.86,0,0,1,2.36,0C184.44,110.25,186.47,115.89,186.33,120.22Z" id="RightEar"></path> <path class="cls-7" d="M186.33,120.23c-.16,5.17-3.46,10.41-7.17,12.34a8.25,8.25,0,0,1-1.55.6.3.3,0,0,1-.13,0,4.55,4.55,0,0,1-2,.06,5,5,0,0,1-2-.86c-.09-.06-.18-.15-.29-.22a6.85,6.85,0,0,1-1.11-1.11c-2.5-3-3.6-8.47-2.28-13.09.09-.33.2-.64.31-1a14.79,14.79,0,0,1,3.23-5,8.91,8.91,0,0,1,4.41-2.58,6,6,0,0,1,2.36,0,6.66,6.66,0,0,1,4.14,3A13.89,13.89,0,0,1,186.33,120.23Z" id="RightEarShadow"></path> <path class="cls-4" d="M84.77,131.06a7.13,7.13,0,0,1-1.24,1.22c0,.05-.11.07-.15.11a5.15,5.15,0,0,1-2,.86,4.9,4.9,0,0,1-2-.06.3.3,0,0,1-.12,0,8.42,8.42,0,0,1-1.57-.6c-3.69-1.93-7-7.17-7.16-12.34a14.13,14.13,0,0,1,1.7-7.17,7,7,0,0,1,4.6-3.7,5.63,5.63,0,0,1,2.35,0A8.65,8.65,0,0,1,83.51,112a14.42,14.42,0,0,1,3.23,5,10.52,10.52,0,0,1,.44,1.48C88.29,123,87.16,128.19,84.77,131.06Z" id="LeftEar"></path> <path class="cls-4" d="M177.68,109.39q-.13,3.28-.6,6.57a78.17,78.17,0,0,1-4,16.21c-4.65,13.05-12.43,24.62-21.83,32.19-.53.42-1.07.84-1.62,1.23a45,45,0,0,1-8.23,4.87c-.09,0-.15.07-.22.09-.29.11-.55.24-.84.35a.61.61,0,0,1-.18.07c-.64.26-1.28.51-1.94.73a2.47,2.47,0,0,1-.36.11.07.07,0,0,1-.06,0,32,32,0,0,1-9.71,1.62h-.56a4.29,4.29,0,0,0-.62,0l-.55,0-.62,0-.33,0c-.15,0-.31,0-.46-.05a32.19,32.19,0,0,1-4.54-.79l-.68-.2c-.14,0-.25-.07-.38-.09s-.51-.16-.75-.22-.36-.11-.51-.18l-.16,0-.73-.27h0l-.66-.27L116,171l-.19-.09h0c-.13-.07-.28-.11-.44-.18s-.29-.13-.44-.2a4.11,4.11,0,0,1-.38-.18l-.44-.19c-.27-.14-.51-.25-.75-.36s-.42-.22-.62-.31-.36-.2-.53-.29a45,45,0,0,1-5.09-3.2c-.62-.45-1.22-.91-1.81-1.37a58.59,58.59,0,0,1-8.54-8.5,76.51,76.51,0,0,1-13.21-23.89c-.15-.35-.26-.73-.4-1.11a77.72,77.72,0,0,1-3.4-14.59c-.18-1.2-.31-2.39-.42-3.61-.09-1-.16-2-.2-3.07,0-.18,0-.34,0-.51a67,67,0,0,1,1-14.71A72.17,72.17,0,0,1,85.94,76.8c4.23-8.87,10.33-17.21,17.94-23.13a39.66,39.66,0,0,1,24.38-8.85c14.86,0,27.16,8.69,35.77,20.44a73.84,73.84,0,0,1,8.6,15.44c.56,1.37,1.09,2.76,1.55,4.16a64.65,64.65,0,0,1,3.5,24.53Z" id="Head"></path> <path class="cls-6" d="M177.52,100.54a30.07,30.07,0,0,1-7.67-1.44l-.4-.15c-7.92-2.73-7.7-7.59-14.2-9.27-7.35-1.88-10.09,3.69-18.14,1.59-6.13-1.59-6-5.18-10.91-5.55-7.36-.56-10.28,7.74-16.48,6.48-4-.82-4.4-4.65-9.29-5.73a13.77,13.77,0,0,0-9.18,1.7h0c2.36-2.28,3-5,6.37-5.84,4.64-1.17,6.17,3.59,12.14,3.52,7.74-.09,9.56-7,16.48-6.48,5,.38,4.78,4,10.91,5.55,8,2.1,10.79-3.49,18.14-1.59,6.5,1.68,6.28,6.55,14.2,9.27a28.56,28.56,0,0,0,7.21,1.55s.13,1.06.42,3.14C177.48,100.34,177.52,100.54,177.52,100.54Z" id="HairsShadow"></path> <path class="cls-5" d="M183,92.6A6.07,6.07,0,0,1,180,94l-.35.07a14.27,14.27,0,0,1-3,.11,28.56,28.56,0,0,1-7.21-1.55c-7.92-2.72-7.7-7.59-14.2-9.27-7.35-1.9-10.09,3.69-18.14,1.59-6.13-1.59-6-5.17-10.91-5.55-6.92-.53-8.74,6.39-16.48,6.48-6,.07-7.5-4.69-12.14-3.52-3.36.84-4,3.56-6.37,5.84a10.12,10.12,0,0,1-2.83,2,15,15,0,0,1-7.61,1.24c-2.81-.26-5.4-1.32-6.71-3.12C70.37,83.2,77.14,73,80,68.6c15.15-22.92,41.49-27.68,44.92-28.23.89-.15,2.06-.33,3.65-.51,8-.86,25.51-1.19,39.6,11C183.29,63.84,189.09,87.11,183,92.6Z" data-name="Hairs" id="Hairs-2"></path> </g> </g></svg>

                        <div class="flex flex-col justify-start items-start m-2">
                            <h2 class="text-xs font-bold text-gray-500">工程部門</h2>
                            <p class="text-md font-bold text-blue-500">陳新直</p>
                        </div>
                    </div>
                    <div class="p-4 flex flex-row justify-start items-center text-center m-2 hover:shadow-lg hover:border-2 hover:border-gray-200 hover:rounded-lg">
                        <!-- 頭像icon -->
                        <svg class="w-12 h-12" viewBox="0 0 256 256" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <defs> <style>.cls-1{fill:#84d0f7;}.cls-2{fill:#aa392d;}.cls-3{fill:#7c211a;}.cls-4{fill:#e59973;}.cls-5{fill:#2d2d2d;}.cls-6{opacity:0.2;}.cls-7{opacity:0.3;}</style> </defs> <g data-name="Male 3" id="Male_3"> <path class="cls-1" d="M250,128.15a121.08,121.08,0,0,1-28.47,78.18,123.13,123.13,0,0,1-9.26,9.85H45.15a101.45,101.45,0,0,1-9-8.91c-.24-.28-.52-.6-.82-1-7-8.31-29.62-37.8-28.45-78.15C8.49,72.26,55.48,6.59,128.44,6.59A121.55,121.55,0,0,1,250,128.15Z" id="Wallpaper"></path> <path class="cls-2" d="M221.54,206.34a130.68,130.68,0,0,1-9.27,9.84,121.2,121.2,0,0,1-83.83,33.54c-39.53,0-69.15-21-83.29-33.54a105.43,105.43,0,0,1-9-8.91c-.22-.29-.51-.6-.82-1C48.89,192.63,67.14,182,88.16,176c5.88,14.64,21.67,25.13,40.25,25.13,17.26,0,32.1-9.05,38.85-22.05.53-1,1-2,1.41-3.08l1.15.33C190.37,182.41,208.22,192.89,221.54,206.34Z" id="Sweater"></path> <path class="cls-3" d="M168.67,176c-.42,1-.88,2.06-1.41,3.08-6.75,13-21.59,22.05-38.85,22.05-18.58,0-34.37-10.49-40.25-25.13.44-.13.88-.24,1.32-.38.22,0,.42-.11.64-.17s.47-.11.69-.18l.22-.07s1.75-.46,3.41-.84a37.76,37.76,0,0,0,67.95,0A28.09,28.09,0,0,1,168.67,176Z" id="Neckband"></path> <path class="cls-4" d="M162.39,174.35a37.76,37.76,0,0,1-67.95,0c1.35-.33,2.7-.64,4.07-.9l1-.22c.17,0,.33-.07.5-.09l.87-.18,1.19-.2c.29-.07.6-.11.91-.18h.09c.33-.86.68-1.79,1-2.81.11-.33.22-.7.33-1.06a35,35,0,0,0,.87-4,41.53,41.53,0,0,0,6.9,4.57c.17.09.35.18.53.29s.42.2.62.31.48.22.75.36l.44.19c.11.07.25.11.38.18s.31.13.44.2.31.11.44.18h0l.19.09c.16.06.34.13.54.19s.42.18.66.27h0l.73.27.16,0c.15.07.33.11.51.18s.51.15.75.22.24.06.38.09l.68.2a36,36,0,0,0,4.54.79c.15,0,.31,0,.46.05l.33,0,.62,0,.55,0a4.29,4.29,0,0,1,.62,0h.56a30.48,30.48,0,0,0,9.71-1.62.07.07,0,0,0,.06,0,2.47,2.47,0,0,0,.36-.11c.79-.27,1.48-.55,2.12-.8l.84-.35c.07,0,.13-.07.22-.09a38.29,38.29,0,0,0,9.85-6.1c.31,1.37.68,2.83,1.15,4.35.42,1.37.86,2.66,1.3,3.87.38.07.73.11,1.11.2s.73.14,1.08.2l1.09.2.79.16c.18,0,.36.06.53.11C159.71,173.73,161.06,174,162.39,174.35Z" id="Neck"></path> <path class="cls-5" d="M197.43,82a7.84,7.84,0,0,1-.35,2.41c-1.6,5.62-7.08,6.19-8.1,11.1-.2.91.09.45,0,3.23-.07,2-.22,6.57-1.53,9.56a10.68,10.68,0,0,1-3.27,4,6.66,6.66,0,0,0-4.14-3,6,6,0,0,0-2.36,0,64.65,64.65,0,0,0-3.5-24.53c-.46-1.4-1-2.79-1.55-4.16A73.84,73.84,0,0,0,164,65.26c-8.61-11.75-20.91-20.49-35.77-20.44a39.66,39.66,0,0,0-24.38,8.85C96.27,59.59,90.17,67.93,85.94,76.8a72.17,72.17,0,0,0-5.86,17.88,67,67,0,0,0-1,14.71,5.63,5.63,0,0,0-2.35,0,7,7,0,0,0-4.6,3.7,10.16,10.16,0,0,1-3.32-3.7c-2.28-4.8,1.11-7.47-1.26-12.43-2.1-4.4-6.08-4.82-7.21-9.91a12.28,12.28,0,0,1-.18-3.78c.53-4.71,3.39-5,4.34-8.74,1.33-5.28-3.5-8.36-1.35-14.86a5.53,5.53,0,0,1,.55-1.37c2.75-4.78,10.27-1.24,14-5.11,3.47-3.56-.25-9.34,4.71-14.2a7.61,7.61,0,0,1,2.1-1.6c5.57-2.7,10.86,4.91,15.79,3,3.94-1.53,2.55-7.06,8.17-10.28a12,12,0,0,1,6.61-1.64c6.37.35,7.92,6.08,12.08,5.53,4-.55,3.78-5.93,9.18-7.66a11.21,11.21,0,0,1,9,1.07c5,3.07,3.52,8.6,7.48,10.08,3.18,1.22,4.93-2.14,11.08-1.79,2,.11,4.8.29,7.21,2,5.55,3.92,2.08,11.06,7.1,14.67,3.52,2.54,7.1.37,12.17,4.33a8.83,8.83,0,0,1,2.7,2.79c2.52,4.49-2.46,8-.82,13.14C193.58,76.63,197.43,77.49,197.43,82Z" id="Hairs"></path> <path class="cls-6" d="M169.82,176.32c-.84,1-1.68,1.86-2.56,2.75a57.77,57.77,0,0,1-6.26,5.44,44,44,0,0,1-9.47,5.37,36,36,0,0,1-6.59,2,29.81,29.81,0,0,1-6,.51A32,32,0,0,1,127.33,190c-.22-.09-.44-.18-.66-.29a41.37,41.37,0,0,1-8.28-4.71,59.34,59.34,0,0,1-12.63-12.81c-.57-.77-1.13-1.57-1.68-2.37.11-.33.22-.7.33-1.06a35,35,0,0,0,.87-4c.59.46,1.19.92,1.81,1.37a45,45,0,0,0,5.09,3.2c.17.09.35.18.53.29s.42.2.62.31.48.22.75.36l.44.19a4.11,4.11,0,0,0,.38.18c.15.07.31.13.44.2s.31.11.44.18h0l.19.09.54.19.66.27h0l.73.27.16,0c.15.07.33.11.51.18s.51.15.75.22.24.06.38.09l.68.2a32.19,32.19,0,0,0,4.54.79c.15,0,.31,0,.46.05l.33,0,.62,0,.55,0a4.29,4.29,0,0,1,.62,0h.56a32,32,0,0,0,9.71-1.62.07.07,0,0,0,.06,0,2.47,2.47,0,0,0,.36-.11c.66-.22,1.3-.47,1.94-.73a.61.61,0,0,0,.18-.07c.29-.11.55-.24.84-.35.07,0,.13-.07.22-.09a45,45,0,0,0,8.23-4.87c.55-.39,1.09-.81,1.62-1.23.31,1.37.68,2.83,1.15,4.35.42,1.37.86,2.66,1.3,3.87.38.07.73.11,1.11.2s.73.14,1.08.2l1.09.2.79.16c.18,0,.36.06.53.11,1.37.28,2.72.57,4,.9,2.12.49,4.23,1,6.28,1.64Z" id="NeckShadow"></path> <path class="cls-4" d="M186.33,120.22c-.15,5.18-3.46,10.42-7.16,12.35a7,7,0,0,1-1.57.6l-.12,0a5.2,5.2,0,0,1-2,.06,5,5,0,0,1-2-.88,1.86,1.86,0,0,1-.28-.21,6.84,6.84,0,0,1-1.12-1.1c-2.67-3.22-3.76-9.25-2-14.06a14.61,14.61,0,0,1,3.23-5,8.82,8.82,0,0,1,4.41-2.59,5.86,5.86,0,0,1,2.36,0C184.44,110.25,186.47,115.89,186.33,120.22Z" id="RightEar"></path> <path class="cls-7" d="M186.33,120.23c-.16,5.17-3.46,10.41-7.17,12.34a8.25,8.25,0,0,1-1.55.6.3.3,0,0,1-.13,0,4.55,4.55,0,0,1-2,.06,5,5,0,0,1-2-.86c-.09-.06-.18-.15-.29-.22a6.85,6.85,0,0,1-1.11-1.11c-2.5-3-3.6-8.47-2.28-13.09.09-.33.2-.64.31-1a14.79,14.79,0,0,1,3.23-5,8.91,8.91,0,0,1,4.41-2.58,6,6,0,0,1,2.36,0,6.66,6.66,0,0,1,4.14,3A13.89,13.89,0,0,1,186.33,120.23Z" id="RightEarShadow"></path> <path class="cls-4" d="M84.77,131.06a7.13,7.13,0,0,1-1.24,1.22c0,.05-.11.07-.15.11a5.15,5.15,0,0,1-2,.86,4.9,4.9,0,0,1-2-.06.3.3,0,0,1-.12,0,8.42,8.42,0,0,1-1.57-.6c-3.69-1.93-7-7.17-7.16-12.34a14.13,14.13,0,0,1,1.7-7.17,7,7,0,0,1,4.6-3.7,5.63,5.63,0,0,1,2.35,0A8.65,8.65,0,0,1,83.51,112a14.42,14.42,0,0,1,3.23,5,10.52,10.52,0,0,1,.44,1.48C88.29,123,87.16,128.19,84.77,131.06Z" id="LeftEar"></path> <path class="cls-4" d="M177.68,109.39q-.13,3.28-.6,6.57a78.17,78.17,0,0,1-4,16.21c-4.65,13.05-12.43,24.62-21.83,32.19-.53.42-1.07.84-1.62,1.23a45,45,0,0,1-8.23,4.87c-.09,0-.15.07-.22.09-.29.11-.55.24-.84.35a.61.61,0,0,1-.18.07c-.64.26-1.28.51-1.94.73a2.47,2.47,0,0,1-.36.11.07.07,0,0,1-.06,0,32,32,0,0,1-9.71,1.62h-.56a4.29,4.29,0,0,0-.62,0l-.55,0-.62,0-.33,0c-.15,0-.31,0-.46-.05a32.19,32.19,0,0,1-4.54-.79l-.68-.2c-.14,0-.25-.07-.38-.09s-.51-.16-.75-.22-.36-.11-.51-.18l-.16,0-.73-.27h0l-.66-.27L116,171l-.19-.09h0c-.13-.07-.28-.11-.44-.18s-.29-.13-.44-.2a4.11,4.11,0,0,1-.38-.18l-.44-.19c-.27-.14-.51-.25-.75-.36s-.42-.22-.62-.31-.36-.2-.53-.29a45,45,0,0,1-5.09-3.2c-.62-.45-1.22-.91-1.81-1.37a58.59,58.59,0,0,1-8.54-8.5,76.51,76.51,0,0,1-13.21-23.89c-.15-.35-.26-.73-.4-1.11a77.72,77.72,0,0,1-3.4-14.59c-.18-1.2-.31-2.39-.42-3.61-.09-1-.16-2-.2-3.07,0-.18,0-.34,0-.51a67,67,0,0,1,1-14.71A72.17,72.17,0,0,1,85.94,76.8c4.23-8.87,10.33-17.21,17.94-23.13a39.66,39.66,0,0,1,24.38-8.85c14.86,0,27.16,8.69,35.77,20.44a73.84,73.84,0,0,1,8.6,15.44c.56,1.37,1.09,2.76,1.55,4.16a64.65,64.65,0,0,1,3.5,24.53Z" id="Head"></path> <path class="cls-6" d="M177.52,100.54a30.07,30.07,0,0,1-7.67-1.44l-.4-.15c-7.92-2.73-7.7-7.59-14.2-9.27-7.35-1.88-10.09,3.69-18.14,1.59-6.13-1.59-6-5.18-10.91-5.55-7.36-.56-10.28,7.74-16.48,6.48-4-.82-4.4-4.65-9.29-5.73a13.77,13.77,0,0,0-9.18,1.7h0c2.36-2.28,3-5,6.37-5.84,4.64-1.17,6.17,3.59,12.14,3.52,7.74-.09,9.56-7,16.48-6.48,5,.38,4.78,4,10.91,5.55,8,2.1,10.79-3.49,18.14-1.59,6.5,1.68,6.28,6.55,14.2,9.27a28.56,28.56,0,0,0,7.21,1.55s.13,1.06.42,3.14C177.48,100.34,177.52,100.54,177.52,100.54Z" id="HairsShadow"></path> <path class="cls-5" d="M183,92.6A6.07,6.07,0,0,1,180,94l-.35.07a14.27,14.27,0,0,1-3,.11,28.56,28.56,0,0,1-7.21-1.55c-7.92-2.72-7.7-7.59-14.2-9.27-7.35-1.9-10.09,3.69-18.14,1.59-6.13-1.59-6-5.17-10.91-5.55-6.92-.53-8.74,6.39-16.48,6.48-6,.07-7.5-4.69-12.14-3.52-3.36.84-4,3.56-6.37,5.84a10.12,10.12,0,0,1-2.83,2,15,15,0,0,1-7.61,1.24c-2.81-.26-5.4-1.32-6.71-3.12C70.37,83.2,77.14,73,80,68.6c15.15-22.92,41.49-27.68,44.92-28.23.89-.15,2.06-.33,3.65-.51,8-.86,25.51-1.19,39.6,11C183.29,63.84,189.09,87.11,183,92.6Z" data-name="Hairs" id="Hairs-2"></path> </g> </g></svg>

                        <div class="flex flex-col justify-start items-start m-2">
                            <h2 class="text-xs font-bold text-gray-500">財務部門</h2>
                            <p class="text-md font-bold text-blue-500">林心如</p>
                        </div>
                    </div>
                    <div class="p-4 flex flex-row justify-start items-center text-center m-2 hover:shadow-lg hover:border-2 hover:border-gray-200 hover:rounded-lg">
                        <!-- 頭像icon -->
                        <svg class="w-12 h-12" viewBox="0 0 256 256" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <defs> <style>.cls-1{fill:#84d0f7;}.cls-2{fill:#aa392d;}.cls-3{fill:#7c211a;}.cls-4{fill:#e59973;}.cls-5{fill:#2d2d2d;}.cls-6{opacity:0.2;}.cls-7{opacity:0.3;}</style> </defs> <g data-name="Male 3" id="Male_3"> <path class="cls-1" d="M250,128.15a121.08,121.08,0,0,1-28.47,78.18,123.13,123.13,0,0,1-9.26,9.85H45.15a101.45,101.45,0,0,1-9-8.91c-.24-.28-.52-.6-.82-1-7-8.31-29.62-37.8-28.45-78.15C8.49,72.26,55.48,6.59,128.44,6.59A121.55,121.55,0,0,1,250,128.15Z" id="Wallpaper"></path> <path class="cls-2" d="M221.54,206.34a130.68,130.68,0,0,1-9.27,9.84,121.2,121.2,0,0,1-83.83,33.54c-39.53,0-69.15-21-83.29-33.54a105.43,105.43,0,0,1-9-8.91c-.22-.29-.51-.6-.82-1C48.89,192.63,67.14,182,88.16,176c5.88,14.64,21.67,25.13,40.25,25.13,17.26,0,32.1-9.05,38.85-22.05.53-1,1-2,1.41-3.08l1.15.33C190.37,182.41,208.22,192.89,221.54,206.34Z" id="Sweater"></path> <path class="cls-3" d="M168.67,176c-.42,1-.88,2.06-1.41,3.08-6.75,13-21.59,22.05-38.85,22.05-18.58,0-34.37-10.49-40.25-25.13.44-.13.88-.24,1.32-.38.22,0,.42-.11.64-.17s.47-.11.69-.18l.22-.07s1.75-.46,3.41-.84a37.76,37.76,0,0,0,67.95,0A28.09,28.09,0,0,1,168.67,176Z" id="Neckband"></path> <path class="cls-4" d="M162.39,174.35a37.76,37.76,0,0,1-67.95,0c1.35-.33,2.7-.64,4.07-.9l1-.22c.17,0,.33-.07.5-.09l.87-.18,1.19-.2c.29-.07.6-.11.91-.18h.09c.33-.86.68-1.79,1-2.81.11-.33.22-.7.33-1.06a35,35,0,0,0,.87-4,41.53,41.53,0,0,0,6.9,4.57c.17.09.35.18.53.29s.42.2.62.31.48.22.75.36l.44.19c.11.07.25.11.38.18s.31.13.44.2.31.11.44.18h0l.19.09c.16.06.34.13.54.19s.42.18.66.27h0l.73.27.16,0c.15.07.33.11.51.18s.51.15.75.22.24.06.38.09l.68.2a36,36,0,0,0,4.54.79c.15,0,.31,0,.46.05l.33,0,.62,0,.55,0a4.29,4.29,0,0,1,.62,0h.56a30.48,30.48,0,0,0,9.71-1.62.07.07,0,0,0,.06,0,2.47,2.47,0,0,0,.36-.11c.79-.27,1.48-.55,2.12-.8l.84-.35c.07,0,.13-.07.22-.09a38.29,38.29,0,0,0,9.85-6.1c.31,1.37.68,2.83,1.15,4.35.42,1.37.86,2.66,1.3,3.87.38.07.73.11,1.11.2s.73.14,1.08.2l1.09.2.79.16c.18,0,.36.06.53.11C159.71,173.73,161.06,174,162.39,174.35Z" id="Neck"></path> <path class="cls-5" d="M197.43,82a7.84,7.84,0,0,1-.35,2.41c-1.6,5.62-7.08,6.19-8.1,11.1-.2.91.09.45,0,3.23-.07,2-.22,6.57-1.53,9.56a10.68,10.68,0,0,1-3.27,4,6.66,6.66,0,0,0-4.14-3,6,6,0,0,0-2.36,0,64.65,64.65,0,0,0-3.5-24.53c-.46-1.4-1-2.79-1.55-4.16A73.84,73.84,0,0,0,164,65.26c-8.61-11.75-20.91-20.49-35.77-20.44a39.66,39.66,0,0,0-24.38,8.85C96.27,59.59,90.17,67.93,85.94,76.8a72.17,72.17,0,0,0-5.86,17.88,67,67,0,0,0-1,14.71,5.63,5.63,0,0,0-2.35,0,7,7,0,0,0-4.6,3.7,10.16,10.16,0,0,1-3.32-3.7c-2.28-4.8,1.11-7.47-1.26-12.43-2.1-4.4-6.08-4.82-7.21-9.91a12.28,12.28,0,0,1-.18-3.78c.53-4.71,3.39-5,4.34-8.74,1.33-5.28-3.5-8.36-1.35-14.86a5.53,5.53,0,0,1,.55-1.37c2.75-4.78,10.27-1.24,14-5.11,3.47-3.56-.25-9.34,4.71-14.2a7.61,7.61,0,0,1,2.1-1.6c5.57-2.7,10.86,4.91,15.79,3,3.94-1.53,2.55-7.06,8.17-10.28a12,12,0,0,1,6.61-1.64c6.37.35,7.92,6.08,12.08,5.53,4-.55,3.78-5.93,9.18-7.66a11.21,11.21,0,0,1,9,1.07c5,3.07,3.52,8.6,7.48,10.08,3.18,1.22,4.93-2.14,11.08-1.79,2,.11,4.8.29,7.21,2,5.55,3.92,2.08,11.06,7.1,14.67,3.52,2.54,7.1.37,12.17,4.33a8.83,8.83,0,0,1,2.7,2.79c2.52,4.49-2.46,8-.82,13.14C193.58,76.63,197.43,77.49,197.43,82Z" id="Hairs"></path> <path class="cls-6" d="M169.82,176.32c-.84,1-1.68,1.86-2.56,2.75a57.77,57.77,0,0,1-6.26,5.44,44,44,0,0,1-9.47,5.37,36,36,0,0,1-6.59,2,29.81,29.81,0,0,1-6,.51A32,32,0,0,1,127.33,190c-.22-.09-.44-.18-.66-.29a41.37,41.37,0,0,1-8.28-4.71,59.34,59.34,0,0,1-12.63-12.81c-.57-.77-1.13-1.57-1.68-2.37.11-.33.22-.7.33-1.06a35,35,0,0,0,.87-4c.59.46,1.19.92,1.81,1.37a45,45,0,0,0,5.09,3.2c.17.09.35.18.53.29s.42.2.62.31.48.22.75.36l.44.19a4.11,4.11,0,0,0,.38.18c.15.07.31.13.44.2s.31.11.44.18h0l.19.09.54.19.66.27h0l.73.27.16,0c.15.07.33.11.51.18s.51.15.75.22.24.06.38.09l.68.2a32.19,32.19,0,0,0,4.54.79c.15,0,.31,0,.46.05l.33,0,.62,0,.55,0a4.29,4.29,0,0,1,.62,0h.56a32,32,0,0,0,9.71-1.62.07.07,0,0,0,.06,0,2.47,2.47,0,0,0,.36-.11c.66-.22,1.3-.47,1.94-.73a.61.61,0,0,0,.18-.07c.29-.11.55-.24.84-.35.07,0,.13-.07.22-.09a45,45,0,0,0,8.23-4.87c.55-.39,1.09-.81,1.62-1.23.31,1.37.68,2.83,1.15,4.35.42,1.37.86,2.66,1.3,3.87.38.07.73.11,1.11.2s.73.14,1.08.2l1.09.2.79.16c.18,0,.36.06.53.11,1.37.28,2.72.57,4,.9,2.12.49,4.23,1,6.28,1.64Z" id="NeckShadow"></path> <path class="cls-4" d="M186.33,120.22c-.15,5.18-3.46,10.42-7.16,12.35a7,7,0,0,1-1.57.6l-.12,0a5.2,5.2,0,0,1-2,.06,5,5,0,0,1-2-.88,1.86,1.86,0,0,1-.28-.21,6.84,6.84,0,0,1-1.12-1.1c-2.67-3.22-3.76-9.25-2-14.06a14.61,14.61,0,0,1,3.23-5,8.82,8.82,0,0,1,4.41-2.59,5.86,5.86,0,0,1,2.36,0C184.44,110.25,186.47,115.89,186.33,120.22Z" id="RightEar"></path> <path class="cls-7" d="M186.33,120.23c-.16,5.17-3.46,10.41-7.17,12.34a8.25,8.25,0,0,1-1.55.6.3.3,0,0,1-.13,0,4.55,4.55,0,0,1-2,.06,5,5,0,0,1-2-.86c-.09-.06-.18-.15-.29-.22a6.85,6.85,0,0,1-1.11-1.11c-2.5-3-3.6-8.47-2.28-13.09.09-.33.2-.64.31-1a14.79,14.79,0,0,1,3.23-5,8.91,8.91,0,0,1,4.41-2.58,6,6,0,0,1,2.36,0,6.66,6.66,0,0,1,4.14,3A13.89,13.89,0,0,1,186.33,120.23Z" id="RightEarShadow"></path> <path class="cls-4" d="M84.77,131.06a7.13,7.13,0,0,1-1.24,1.22c0,.05-.11.07-.15.11a5.15,5.15,0,0,1-2,.86,4.9,4.9,0,0,1-2-.06.3.3,0,0,1-.12,0,8.42,8.42,0,0,1-1.57-.6c-3.69-1.93-7-7.17-7.16-12.34a14.13,14.13,0,0,1,1.7-7.17,7,7,0,0,1,4.6-3.7,5.63,5.63,0,0,1,2.35,0A8.65,8.65,0,0,1,83.51,112a14.42,14.42,0,0,1,3.23,5,10.52,10.52,0,0,1,.44,1.48C88.29,123,87.16,128.19,84.77,131.06Z" id="LeftEar"></path> <path class="cls-4" d="M177.68,109.39q-.13,3.28-.6,6.57a78.17,78.17,0,0,1-4,16.21c-4.65,13.05-12.43,24.62-21.83,32.19-.53.42-1.07.84-1.62,1.23a45,45,0,0,1-8.23,4.87c-.09,0-.15.07-.22.09-.29.11-.55.24-.84.35a.61.61,0,0,1-.18.07c-.64.26-1.28.51-1.94.73a2.47,2.47,0,0,1-.36.11.07.07,0,0,1-.06,0,32,32,0,0,1-9.71,1.62h-.56a4.29,4.29,0,0,0-.62,0l-.55,0-.62,0-.33,0c-.15,0-.31,0-.46-.05a32.19,32.19,0,0,1-4.54-.79l-.68-.2c-.14,0-.25-.07-.38-.09s-.51-.16-.75-.22-.36-.11-.51-.18l-.16,0-.73-.27h0l-.66-.27L116,171l-.19-.09h0c-.13-.07-.28-.11-.44-.18s-.29-.13-.44-.2a4.11,4.11,0,0,1-.38-.18l-.44-.19c-.27-.14-.51-.25-.75-.36s-.42-.22-.62-.31-.36-.2-.53-.29a45,45,0,0,1-5.09-3.2c-.62-.45-1.22-.91-1.81-1.37a58.59,58.59,0,0,1-8.54-8.5,76.51,76.51,0,0,1-13.21-23.89c-.15-.35-.26-.73-.4-1.11a77.72,77.72,0,0,1-3.4-14.59c-.18-1.2-.31-2.39-.42-3.61-.09-1-.16-2-.2-3.07,0-.18,0-.34,0-.51a67,67,0,0,1,1-14.71A72.17,72.17,0,0,1,85.94,76.8c4.23-8.87,10.33-17.21,17.94-23.13a39.66,39.66,0,0,1,24.38-8.85c14.86,0,27.16,8.69,35.77,20.44a73.84,73.84,0,0,1,8.6,15.44c.56,1.37,1.09,2.76,1.55,4.16a64.65,64.65,0,0,1,3.5,24.53Z" id="Head"></path> <path class="cls-6" d="M177.52,100.54a30.07,30.07,0,0,1-7.67-1.44l-.4-.15c-7.92-2.73-7.7-7.59-14.2-9.27-7.35-1.88-10.09,3.69-18.14,1.59-6.13-1.59-6-5.18-10.91-5.55-7.36-.56-10.28,7.74-16.48,6.48-4-.82-4.4-4.65-9.29-5.73a13.77,13.77,0,0,0-9.18,1.7h0c2.36-2.28,3-5,6.37-5.84,4.64-1.17,6.17,3.59,12.14,3.52,7.74-.09,9.56-7,16.48-6.48,5,.38,4.78,4,10.91,5.55,8,2.1,10.79-3.49,18.14-1.59,6.5,1.68,6.28,6.55,14.2,9.27a28.56,28.56,0,0,0,7.21,1.55s.13,1.06.42,3.14C177.48,100.34,177.52,100.54,177.52,100.54Z" id="HairsShadow"></path> <path class="cls-5" d="M183,92.6A6.07,6.07,0,0,1,180,94l-.35.07a14.27,14.27,0,0,1-3,.11,28.56,28.56,0,0,1-7.21-1.55c-7.92-2.72-7.7-7.59-14.2-9.27-7.35-1.9-10.09,3.69-18.14,1.59-6.13-1.59-6-5.17-10.91-5.55-6.92-.53-8.74,6.39-16.48,6.48-6,.07-7.5-4.69-12.14-3.52-3.36.84-4,3.56-6.37,5.84a10.12,10.12,0,0,1-2.83,2,15,15,0,0,1-7.61,1.24c-2.81-.26-5.4-1.32-6.71-3.12C70.37,83.2,77.14,73,80,68.6c15.15-22.92,41.49-27.68,44.92-28.23.89-.15,2.06-.33,3.65-.51,8-.86,25.51-1.19,39.6,11C183.29,63.84,189.09,87.11,183,92.6Z" data-name="Hairs" id="Hairs-2"></path> </g> </g></svg>

                        <div class="flex flex-col justify-start items-start m-2">
                            <h2 class="text-xs font-bold text-gray-500">業務部門</h2>
                            <p class="text-md font-bold text-blue-500">馬新衣</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white shadow-lg mt-auto">
        <div class="container mx-auto px-4 py-6">
            <p class="text-center text-gray-600">© 2024 益芯能源工程-管理系統. All rights reserved.</p>
        </div>
    </footer>

    <script src="employee_Service.js"></script>
    <script>
        // Function to get URL parameters
        function getQueryParam(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        // Function to populate data into the HTML
        function populateData(employeeData) {
            if (!employeeData) return;

            // Populate avatar and header info
            const firstChar = employeeData.name ? employeeData.name.charAt(0) : '';
            const avatarCircle = document.querySelector('.w-32.h-32.rounded-full.mx-auto');
            if (avatarCircle) {
                const span = avatarCircle.querySelector('span');
                if(span) span.textContent = firstChar;
            }
            
            const nameAvatar = document.getElementById('name_avatar');
            if(nameAvatar) name_avatar.textContent = employeeData.name || 'N/A';

            const empNoAvatar = document.getElementById('empNo_avatar');
            if(empNoAvatar) empNo_avatar.textContent = `員工編號: ${employeeData.empNo || 'N/A'}`;

            const departmentAvatar = document.getElementById('department_avatar');
            if(departmentAvatar) department_avatar.textContent = `${employeeData.department || 'N/A'}`;

            const positionAvatar = document.getElementById('position_avatar');
            if(positionAvatar) position_avatar.textContent = `${employeeData.position || 'N/A'}`;

            const startDateAvatar = document.getElementById('startDate_avatar');
            if(startDateAvatar) startDate_avatar.textContent = `${employeeData.startDate || 'N/A'}`;



            // Populate all fields by ID
            for (const key in employeeData) {
                const elements = document.querySelectorAll(`#${key}`);
                elements.forEach(element => {
                    if (element) {
                        // Format currency fields
                        if (['salary', 'supervisorAllowance', 'mealAllowance', 'companyLaborInsurance', 'companyHealthInsurance', 'companyRetirementInsurance', 'employeeLaborInsurance', 'employeeHealthInsurance', 'employeeRetirementInsurance'].includes(key)) {
                             element.textContent = employeeData[key] ? `NT$ ${parseInt(employeeData[key]).toLocaleString()}` : 'NT$ 0';
                        } else {
                            element.textContent = employeeData[key] || 'N/A';
                        }
                    }
                });
            }
        }
        
        // Main function to load employee data
        async function loadEmployeeDetails() {
            const employeeId = getQueryParam('id');
            if (!employeeId) {
                alert('未指定員工ID');
                document.querySelector('main').innerHTML = '<p class="text-center text-red-500">錯誤：未指定員工ID。</p>';
                return;
            }

            try {
                const emp = await getEmployeeById(employeeId);
                const departments = await getDepartments();
                const positions = await getPositions();
                departmentsMap = new Map(departments.map(dept => [dept.id, dept.name]));
                positionsMap = new Map(positions.map(pos => [pos.id, pos.name]));
                const employeeData = {
                    ...emp,
                    department: departmentsMap.get(emp.departmentId),
                    position: positionsMap.get(emp.positionId)
                };

                if (employeeData) {
                    console.log('員工資料:', employeeData);
                    populateData(employeeData);
                } else {
                    alert('找不到該員工資料');
                    document.querySelector('main').innerHTML = `<p class="text-center text-red-500">錯誤：找不到ID為 ${employeeId} 的員工資料。</p>`;
                }
            } catch (error) {
                console.error('獲取員工資料失敗:', error);
                alert('獲取員工資料失敗');
                document.querySelector('main').innerHTML = '<p class="text-center text-red-500">系統發生錯誤，無法載入員工資料。</p>';
            }
        }

        function setupTabs() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Deactivate all buttons
                    tabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600');
                        btn.classList.add('text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300', 'border-transparent');
                    });
                    // Activate the clicked button
                    button.classList.add('text-blue-600', 'border-blue-600');
                    button.classList.remove('text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300', 'border-transparent');

                    // Hide all panels
                    tabPanels.forEach(panel => {
                        panel.classList.add('hidden');
                    });
                    // Show the target panel
                    const targetPanel = document.getElementById(button.dataset.tab);
                    if(targetPanel) {
                        targetPanel.classList.remove('hidden');
                    }
                });
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            loadEmployeeDetails();
            setupTabs();
        });
    </script>

</body>
</html>
