# ViewEmployeePage 編輯功能測試指南

## 測試前準備

1. 確保資料庫中有員工、部門和職位資料
2. 確保所有相關的 JavaScript 檔案都已正確載入
3. 開啟瀏覽器開發者工具以監控錯誤

## 測試步驟

### 1. 基本功能測試

#### 1.1 開啟編輯模態框
- 開啟 ViewEmployeePage.html?id=[員工ID]
- 點擊右上角的「編輯」按鈕
- **預期結果**：模態框應該開啟並顯示當前員工資料

#### 1.2 檢查資料填充
- 確認所有欄位都正確填入當前員工的資料
- 檢查部門和職位下拉選單是否正確載入選項
- **預期結果**：所有資料應該正確顯示

#### 1.3 關閉模態框
- 點擊右上角的 X 按鈕或「取消」按鈕
- **預期結果**：模態框應該關閉

### 2. 資料驗證測試

#### 2.1 必填欄位驗證
測試以下必填欄位：
- 清空「員工編號」欄位並嘗試儲存
- 清空「姓名」欄位並嘗試儲存
- 清空「身分證號」欄位並嘗試儲存
- 清空「部門」選項並嘗試儲存
- 清空「職位」選項並嘗試儲存
- 清空「到職日期」並嘗試儲存
- 清空「基本薪資」並嘗試儲存

**預期結果**：每次都應該顯示相應的錯誤訊息

#### 2.2 格式驗證測試

**身分證號驗證：**
- 輸入無效格式：`123456789`
- 輸入錯誤檢查碼：`A123456780`
- 輸入正確格式：`A123456789`

**電子郵件驗證：**
- 輸入無效格式：`invalid-email`
- 輸入正確格式：`<EMAIL>`

**數值驗證：**
- 在薪資欄位輸入負數：`-1000`
- 輸入正確數值：`30000`

#### 2.3 邏輯驗證測試
- 設定離職日期早於到職日期
- **預期結果**：應該顯示錯誤訊息

### 3. 資料儲存測試

#### 3.1 成功儲存測試
1. 修改員工姓名
2. 修改部門
3. 修改薪資
4. 點擊「儲存」按鈕
5. **預期結果**：
   - 顯示成功訊息
   - 模態框關閉
   - 頁面資料重新載入並顯示更新後的資料

#### 3.2 在職狀態自動更新測試
1. 設定離職日期
2. 儲存資料
3. **預期結果**：員工狀態應該自動變更為「離職」

1. 清空離職日期
2. 儲存資料
3. **預期結果**：員工狀態應該自動變更為「在職」

### 4. 下拉選單測試

#### 4.1 部門下拉選單
- 確認顯示所有可用部門
- 選擇不同部門並儲存
- **預期結果**：部門應該正確更新

#### 4.2 職位下拉選單
- 確認顯示所有可用職位
- 選擇不同職位並儲存
- **預期結果**：職位應該正確更新

### 5. 錯誤處理測試

#### 5.1 網路錯誤模擬
- 在開發者工具中模擬網路離線
- 嘗試儲存資料
- **預期結果**：應該顯示適當的錯誤訊息

#### 5.2 資料庫錯誤模擬
- 修改員工編號為已存在的編號
- 嘗試儲存
- **預期結果**：應該顯示錯誤訊息

## 常見問題排除

### 問題 1：模態框無法開啟
**可能原因：**
- JavaScript 載入錯誤
- DOM 元素未正確載入

**檢查方法：**
- 查看瀏覽器控制台是否有錯誤訊息
- 確認 `currentEmployeeData` 是否有值

### 問題 2：下拉選單沒有選項
**可能原因：**
- 部門或職位資料未正確載入
- 資料庫連接問題

**檢查方法：**
- 查看 `allDepartments` 和 `allPositions` 變數是否有資料
- 檢查資料庫中是否有部門和職位資料

### 問題 3：資料無法儲存
**可能原因：**
- 資料驗證失敗
- 資料庫寫入權限問題
- 網路連接問題

**檢查方法：**
- 查看控制台錯誤訊息
- 確認資料格式是否正確
- 檢查網路連接狀態

### 問題 4：頁面資料未更新
**可能原因：**
- `loadEmployeeDetails()` 函數未正確執行
- 快取問題

**檢查方法：**
- 重新整理頁面
- 清除瀏覽器快取

## 測試完成檢查清單

- [ ] 編輯按鈕正常工作
- [ ] 模態框正確開啟和關閉
- [ ] 所有欄位正確填充資料
- [ ] 必填欄位驗證正常
- [ ] 格式驗證正常
- [ ] 邏輯驗證正常
- [ ] 資料成功儲存
- [ ] 在職狀態自動更新
- [ ] 下拉選單正常工作
- [ ] 錯誤處理正常
- [ ] 頁面資料正確更新

## 效能測試

- 測試大量資料時的載入速度
- 測試表單驗證的響應時間
- 測試資料儲存的速度

完成所有測試項目後，編輯功能應該能夠穩定運行並符合業務需求。
