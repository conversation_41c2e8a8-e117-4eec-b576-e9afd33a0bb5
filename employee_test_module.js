/**
 * @file 員工資料測試模組
 * @description 提供一個函式，用於生成並新增隨機的員工資料以供測試。
 */

/**
 * 新增指定數量的隨機員工資料。
 * 
 * 如何使用：
 * 1. 在瀏覽器中打開員工管理頁面。
 * 2. 按下 F12 打開開發者工具。
 * 3. 切換到 "Console" (主控台) 標籤。
 * 4. 輸入 `addRandomEmployees(15)` 然後按下 Enter，即可新增 15 筆隨機員工。
 * 
 * @param {number} count - 要新增的員工數量，預設為 10。
 */
async function addRandomEmployees(count = 10) {
    console.log(`準備新增 ${count} 筆隨機員工資料...`);

    try {
        // 獲取現有的部門和職位，以確保關聯資料的正確性
        const [departments, positions] = await Promise.all([
            getAllDepartments(),
            getAllPositions()
        ]);

        if (departments.length === 0 || positions.length === 0) {
            const message = departments.length === 0 
                ? "無法新增員工，因為資料庫中沒有任何部門資料。請先在『實體管理』頁面新增部門。"
                : "無法新增員工，因為資料庫中沒有任何職位資料。請先在『實體管理』頁面新增職位。";
            alert(message);
            console.error(message);
            return;
        }

        // 預定義一些隨機的姓氏和名字
        const lastNames = ['陳', '林', '黃', '張', '李', '王', '吳', '劉', '蔡', '楊'];
        const firstNames = ['冠廷', '雅婷', '家豪', '宜臻', '承翰', '詩涵', '宇翔', '佳穎', '俊彥', '怡君'];

        const employeePromises = [];

        for (let i = 0; i < count; i++) {
            const randomLastName = lastNames[Math.floor(Math.random() * lastNames.length)];
            const randomFirstName = firstNames[Math.floor(Math.random() * firstNames.length)];
            const fullName = `${randomLastName}${randomFirstName}`;

            const randomDepartment = departments[Math.floor(Math.random() * departments.length)];
            const randomPosition = positions[Math.floor(Math.random() * positions.length)];
            
            const empNo = `EMP${String(Date.now()).slice(-4) + String(i).padStart(3, '0')}`;
            const idNumber = `A1${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`;

            const employeeData = {
                name: fullName,
                empNo: empNo,
                idNumber: idNumber,
                departmentId: randomDepartment.id,
                positionId: randomPosition.id,
                phone: `09${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
                email: `${empNo.toLowerCase()}@example.com`,
                address: `測試地址${i + 1}號`,
                salary: Math.floor(Math.random() * (60000 - 35000) + 35000),
                supervisorAllowance: Math.random() > 0.7 ? Math.floor(Math.random() * (8000 - 2000) + 2000) : 0,
                mealAllowance: 2400,
                companyLaborInsurance: Math.floor(Math.random() * (2000 - 1500) + 1500),
                companyHealthInsurance: Math.floor(Math.random() * (1800 - 1200) + 1200),
                companyRetirementInsurance: Math.floor(Math.random() * (3000 - 2500) + 2500),
                employeeLaborInsurance: Math.floor(Math.random() * (800 - 500) + 500),
                employeeHealthInsurance: Math.floor(Math.random() * (700 - 400) + 400),
                employeeRetirementInsurance: 0,
                startDate: `2023/${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}/${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
                resignationDate: '',
                note: '此為隨機生成的測試資料。',
                status: '在職',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            };

            // 將儲存操作加入到 Promise 陣列中
            employeePromises.push(saveEmployee(employeeData));
        }

        // 等待所有員工資料都儲存完畢
        await Promise.all(employeePromises);

        // 重新整理頁面上的員工列表
        await refreshEmployeeList();

        showSuccessMessage(`${count} 筆員工資料新增成功！`);
        console.log(`${count} 筆員工資料已成功新增至資料庫。`);

    } catch (error) {
        console.error('新增隨機員工時發生錯誤：', error);
        alert('新增隨機員工失敗，請查看主控台以獲取更多資訊。');
    }
}
