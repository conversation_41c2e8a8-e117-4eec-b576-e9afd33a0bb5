<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>員工資料管理 - 益芯能源工程-管理系統</title>
    <link rel="icon" href="../../common/img/logo.png" type="image/x-icon">
    <script type="importmap">
    {
        "imports": {
        "@common/": "/common/"
        }
    }
    </script>
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-analytics-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-auth-compat.js"></script>
    <script src="../../common/firebaseAPI/auth.js"></script>
    <script src="../../common/db/db.js"></script>
    <script src="../../common/db/preload.js"></script>
    <script src="../../common/utils/CommonUtils.js"></script>
    <script src="../../common/utils/pageTransfer.js"></script>
    <script src="../../common/utils/ModalUtils.js"></script>
    <style>
        .nav-item:hover>.submenu {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            top: 100%;
            left: 0;
        }

        .submenu .submenu {
            top: 0;
            left: 100%;
        }

        .nav-item {
            position: relative;
        }
    </style>
    <script src="../../common/navigation/navigation.js"></script>
</head>

<body class="flex flex-col min-h-screen bg-gray-100">


    <main class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">員工資料管理</h1>
            <p class="text-gray-600">管理員工基本資料與薪資設定</p>
        </div>

        <!-- 新增員工按鈕 -->
        <div class="mb-4">
            <button class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                新增員工
            </button>
        </div>

        <!-- 搜尋和過濾 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-4">
            <form class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">員工編號</label>
                    <input type="text" name="searchId"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">員工姓名</label>
                    <input type="text" name="searchName"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">部門</label>
                    <select name="searchDepartment" id="searchDepartment"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="全部">全部</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        搜尋
                    </button>
                </div>
            </form>
        </div>

        <!-- 員工卡片列表 -->
        <div id="employee-cards-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- 員工卡片將由 JavaScript 動態生成於此 -->
        </div>

        <!-- 分頁控制 -->
        <div id="pagination-controls" class="flex justify-center items-center space-x-4 mt-8">
            <!-- 分頁按鈕將由 JavaScript 動態生成於此 -->
        </div>
    </main>

    <!-- 新增/編輯員工模態框 -->
    <div id="employeeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h1 id="employeeModalTitle" class="text-3xl font-medium font-bold text-gray-900">新增員工</h1>
                <button class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="space-y-4">
                <div class="items-center mb-6">
                    <div class="items-center border rounded-md bg-white m-4 p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-bold text-gray-800">基本資料</h2>
                        </div>
                        <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">員工編號*</label>
                            <input type="text" name="empNo"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">姓名*</label>
                            <input type="text" name="name"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">身分證號*</label>
                            <input type="text" name="idNumber"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">部門*</label>
                                <select name="department" id="department"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">請選擇部門</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">職位*</label>
                                <select name="position" id="position"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">請選擇職位</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">電話</label>
                                <input type="tel" name="phone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">電子郵件</label>
                                <input type="email" name="email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">地址</label>
                            <input type="text" name="address" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">到職日期*</label>
                            <input type="date" name="startDate"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">離職日期</label>
                            <input type="date" name="resignationDate"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">備註</label>
                            <textarea name="note" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                        </div>
                    </div>
                    <div class="items-center border rounded-md bg-white m-4 p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-bold text-gray-800">薪資結構</h2>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">基本薪資*</label>
                            <input type="number" name="salary"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">主管津貼</label>
                            <input type="number" name="supervisorAllowance"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mt-4">餐食津貼</label>
                            <input type="number" name="mealAllowance"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="items-center border rounded-md bg-white m-4 p-6">
                            <div class="flex justify-between items-center mb-6">
                                <h2 class="text-xl font-bold text-gray-800">勞健保公司負擔</h2>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">勞工保險</label>
                                <input type="number" name="companyLaborInsurance"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">健康保險</label>
                                <input type="number" name="companyHealthInsurance"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">公司提撥6%勞工退休金</label>
                                <input type="number" name="companyRetirementInsurance"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>
                        <div class="items-center border rounded-md bg-white m-4 p-6">
                            <div class="flex justify-between items-center mb-6">
                                <h2 class="text-xl font-bold text-gray-800">勞健保員工自負</h2>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">勞工保險</label>
                                <input type="number" name="employeeLaborInsurance"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">健康保險</label>
                                <input type="number" name="employeeHealthInsurance"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mt-4">自願提撥勞工退休金</label>
                                <input type="number" name="employeeRetirementInsurance"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideAddEmployeeModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        儲存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <footer class="bg-white shadow-lg mt-auto">
        <div class="container mx-auto px-4 py-6">
            <p class="text-center text-gray-600">© 2024 益芯能源工程-管理系統. All rights reserved.</p>
        </div>
    </footer>

    <script src="employee_Service.js"></script>
    <script src="employee_UI.js"></script>
    <script src="employee_LogicManager.js"></script>
    <script src="employee_Controller.js"></script>
    <script src="employee_test_module.js"></script>
</body>

</html>