# ViewEmployeePage 編輯功能實作說明

## 功能概述

已成功為 ViewEmployeePage.html 實作完整的員工資料編輯功能，嚴格遵循 `參考用db.js` 中定義的員工資料表欄位規範。

## 實作的功能

### 1. 編輯按鈕
- 在頁面右上角加入「編輯」按鈕
- 點擊後開啟編輯模態框

### 2. 編輯模態框
包含以下區塊：

#### 基本資料區塊
- 員工編號 (必填)
- 姓名 (必填)
- 身分證號 (必填)
- 電話
- 電子郵件
- 地址

#### 職務資料區塊
- 部門下拉選單 (必填)
- 職位下拉選單 (必填)
- 到職日期 (必填)
- 離職日期

#### 薪資資料區塊
- 基本薪資 (必填)
- 主管津貼
- 餐費津貼

#### 保險費用區塊
**公司負擔：**
- 勞工保險
- 健康保險
- 勞工退休金

**員工負擔：**
- 勞工保險
- 健康保險
- 自願提撥勞退

#### 備註區塊
- 備註欄位

### 3. 資料驗證
實作嚴謹的資料驗證機制：

#### 必填欄位檢查
- 員工編號
- 姓名
- 身分證號
- 部門
- 職位
- 到職日期
- 基本薪資

#### 格式驗證
- 身分證號：台灣身分證號格式驗證（包含檢查碼）
- 電子郵件：標準電子郵件格式驗證
- 數值欄位：確保薪資和保險費用為非負數

#### 邏輯驗證
- 離職日期必須晚於到職日期
- 自動根據離職日期設定在職狀態

### 4. 下拉選單資料
- 部門下拉選單：從資料庫動態載入所有部門資料
- 職位下拉選單：從資料庫動態載入所有職位資料

### 5. 資料儲存
- 使用現有的 `saveEmployee` 函數進行資料更新
- 自動設定 `updatedAt` 時間戳記
- 根據離職日期自動更新在職狀態

## 技術實作細節

### 新增的 JavaScript 函數

1. **setupEditButton()** - 設定編輯按鈕事件監聽器
2. **openEditModal()** - 開啟編輯模態框
3. **closeEditModal()** - 關閉編輯模態框
4. **populateDropdowns()** - 填充部門和職位下拉選單
5. **fillEditForm()** - 填充編輯表單資料
6. **collectFormData()** - 收集表單資料
7. **validateEmployeeData()** - 驗證員工資料
8. **validateIdNumber()** - 身分證號驗證
9. **validateEmail()** - 電子郵件驗證

### 資料庫欄位對應

嚴格按照 `參考用db.js` 中 `STORE_FIELDS.employees` 的定義：

```javascript
// 必填欄位 (required: true)
- empNo (員工編號)
- name (姓名)
- idNumber (身分證字號)
- departmentId (部門ID)
- positionId (職位ID)
- startDate (到職日期)
- salary (薪資)
- status (在職狀態)

// 選填欄位 (required: false)
- email, phone, address
- supervisorAllowance, mealAllowance
- 各項保險費用
- resignationDate, note
```

### 資料類型處理

- **text 類型**：字串處理，去除前後空白
- **number 類型**：數值處理，確保為數字格式
- **date 類型**：日期格式驗證

## 使用方式

1. 開啟 ViewEmployeePage.html 並傳入員工 ID 參數
2. 點擊右上角的「編輯」按鈕
3. 在模態框中修改員工資料
4. 點擊「儲存」按鈕完成更新
5. 系統會自動驗證資料並更新到資料庫

## 錯誤處理

- 資料驗證失敗時會顯示詳細錯誤訊息
- 儲存失敗時會顯示錯誤提示
- 成功儲存後會重新載入頁面資料並顯示成功訊息

## 相容性

- 完全相容現有的資料庫結構
- 使用現有的服務函數和工具函數
- 保持與其他頁面的一致性
