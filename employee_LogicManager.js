// Business logic for employee management

/**
 * @description 驗證員工表單資料
 * @param {Object} data - 從表單獲取的員工資料
 * @returns {string|null} 如果驗證失敗，返回錯誤訊息；否則返回 null
 */
function validateEmployeeForm(data) {
    if (!data.empNo || data.empNo.trim() === '') {
        return '請輸入員工編號！';
    }
    if (!data.name || data.name.trim() === '') {
        return '請輸入員工姓名！';
    }
    if (!data.departmentId || data.departmentId === '') {
        return '請選擇部門！';
    }
    if (!data.positionId || data.positionId === '') {
        return '請選擇職位！';
    }
    if (!data.salary || isNaN(data.salary) || data.salary <= 0) {
        return '請輸入有效的薪資金額！';
    }
    if (!data.startDate) {
        return '請選擇到職日期！';
    }
    const startDate = new Date(data.startDate.replace(/\//g, '-'));
    if (isNaN(startDate.getTime())) {
        return '請輸入有效的到職日期！';
    }
    if (data.resignationDate && data.resignationDate.trim() !== '') {
        const resignationDate = new Date(data.resignationDate.replace(/\//g, '-'));
        if (isNaN(resignationDate.getTime())) {
            return '請輸入有效的離職日期！';
        }
    }
    if (!data.idNumber || data.idNumber.trim() === '') {
        return '請輸入身分證號！';
    }
    const idNumberPattern = /^[A-Z][12]\d{8}$/;
    if (!idNumberPattern.test(data.idNumber)) {
        return '身分證號格式不正確！\n格式說明：第一碼為大寫英文字母，第二碼為1或2，後面接8位數字';
    }
    return null; // No validation errors
}

/**
 * @description 根據搜尋條件過濾員工
 * @param {Array} employees - 完整的員工列表
 * @param {Object} criteria - 搜尋條件物件
 * @param {string} criteria.searchId - 員工編號
 * @param {string} criteria.searchName - 員工姓名
 * @param {string} criteria.searchDepartment - 部門 ID
 * @returns {Array} 過濾後的員工列表
 */
function filterEmployees(employees, { searchId, searchName, searchDepartment }) {
    return employees.filter(emp => {
        const matchId = !searchId || emp.empNo.toLowerCase().includes(searchId.toLowerCase());
        const matchName = !searchName || emp.name.toLowerCase().includes(searchName.toLowerCase());
        const matchDepartment = searchDepartment === '全部' || String(emp.departmentId) === String(searchDepartment);
        return matchId && matchName && matchDepartment;
    });
}

/**
 * @description 處理儲存前的員工資料
 * @param {Object} formData - 從表單獲取的原始資料
 * @returns {Object} 處理過的員工資料
 */
function processEmployeeData(formData) {
    formData.status = formData.resignationDate ? '離職' : '在職';
    formData.updatedAt = CommonUtils.formatDate(new Date(), 'YYYY-MM-DD');
    return formData;
}
