
// Service layer for employee-related data operations

/**
 * @description 獲取所有員工資料
 * @returns {Promise<Array>} 員工資料陣列
 */
async function getAllEmployees() {
    return await getAllData('employees');
}

/**
 * @description 根據 ID 獲取單一員工資料
 * @param {string} employeeId - 員工 ID
 * @returns {Promise<Object>} 員工資料物件
 */
async function getEmployeeById(employeeId) {
    // This function assumes you have a function to get a single document by ID
    // You might need to implement this in your db.js
    return await getDataById('employees', employeeId);
}

/**
 * @description 儲存員工資料 (新增或更新)
 * @param {Object} employeeData - 員工資料物件
 */
async function saveEmployee(employeeData) {
    if (employeeData.id) {
        // Update existing employee
        await updateData('employees', employeeData);
    } else {
        // Add new employee
        employeeData.createdAt = new Date().toISOString();
        await addData('employees', employeeData);
    }
}

/**
 * @description 根據 ID 刪除員工資料
 * @param {string} employeeId - 員工 ID
 */
async function deleteEmployeeById(employeeId) {
    await deleteData('employees', employeeId);
}

/**
 * @description 獲取所有部門資料
 * @returns {Promise<Array>} 部門資料陣列
 */
async function getAllDepartments() {
    return await getAllData('departments');
}

/**
 * @description 獲取所有職位資料
 * @returns {Promise<Array>} 職位資料陣列
 */
async function getAllPositions() {
    return await getAllData('positions');
}
