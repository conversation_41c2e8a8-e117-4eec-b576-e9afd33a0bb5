// 資料庫名稱和版本
const DB_NAME = 'YIXIN_financeDB_OFFLINE';
const DB_VERSION = 1;



// 資料表名稱
const STORE_NAMES = {
    accountTypes: 'accountTypes',
    accountingItems: 'accountingItems',
    accounts: 'accounts',
    advances: 'advances',
    advanceDetails: 'advanceDetails',
    advance_types: 'advance_types',
    banks: 'banks',
    departments: 'departments',
    employees: 'employees',
    entities: 'entities',
    entity_subtypes: 'entity_subtypes',
    entity_types: 'entity_types',
    paymentMethods: 'paymentMethods',
    positions: 'positions',
    projectBudgetCosts: 'projectBudgetCosts',
    projectContractPayments: 'projectContractPayments',
    projectTags: 'projectTags',
    projects: 'projects',
    projectTasks: 'projectTasks',
    salaries: 'salaries',
    salaryDetails: 'salaryDetails',
    salaryTypes: 'salaryTypes',
    supplier_types: 'supplier_types',
    taxTypesRates: 'taxTypesRates',
    transactionCategories: 'transactionCategories',
    transactionCategoryItems: 'transactionCategoryItems',
    transactionDetails: 'transactionDetails',
    transactions: 'transactions',
    transactionTags: 'transactionTags',
    assets: 'assets', // 新增資產資料表
    assetDisposals: 'assetDisposals', // 新增資產處分資料表
    journalEntries: 'journalEntries', // 新增會計分錄資料表
    companySettings: 'companySettings' // 新增公司設定資料表
};

// 資料表設定
const STORE_CONFIG = {
    accountTypes: { keyPath: 'id', autoIncrement: true },
    accountingItems: { keyPath: 'id', autoIncrement: true },
    accounts: { keyPath: 'id', autoIncrement: true },
    advances: { keyPath: 'id', autoIncrement: true },
    advanceDetails: { keyPath: 'id', autoIncrement: true },
    advance_types: { keyPath: 'id', autoIncrement: true },
    banks: { keyPath: 'id', autoIncrement: true },
    departments: { keyPath: 'id', autoIncrement: true },
    employees: { keyPath: 'id', autoIncrement: true },
    entities: { keyPath: 'id', autoIncrement: true },
    entity_subtypes: { keyPath: 'id', autoIncrement: true },
    entity_types: { keyPath: 'id', autoIncrement: true },
    paymentMethods: { keyPath: 'id', autoIncrement: true },
    positions: { keyPath: 'id', autoIncrement: true },
    projectBudgetCosts: { keyPath: 'id', autoIncrement: true },
    projectContractPayments: { keyPath: 'id', autoIncrement: true },
    projectTags: { keyPath: 'id', autoIncrement: true },
    projects: { keyPath: 'id', autoIncrement: true },
    projectTasks: { keyPath: 'id', autoIncrement: true },
    salaries: { keyPath: 'id', autoIncrement: true },
    salaryDetails: { keyPath: 'id', autoIncrement: true },
    salaryTypes: { keyPath: 'id', autoIncrement: true },
    supplier_types: { keyPath: 'id', autoIncrement: true },
    taxTypesRates: { keyPath: 'id', autoIncrement: true },
    transactionCategories: { keyPath: 'id', autoIncrement: true },
    transactionCategoryItems: { keyPath: 'id', autoIncrement: true },
    transactionDetails: { keyPath: 'id', autoIncrement: true },
    transactions: { keyPath: 'id', autoIncrement: true },
    transactionTags: { keyPath: 'id', autoIncrement: true },
    assets: { keyPath: 'id', autoIncrement: true }, // 新增資產資料表設定
    assetDisposals: { keyPath: 'id', autoIncrement: true }, // 新增資產處分資料表設定
    journalEntries: { keyPath: 'id', autoIncrement: true }, // 新增會計分錄資料表設定
    companySettings: { keyPath: 'id', autoIncrement: false } // 新增公司設定資料表設定
};

// 定義資料表標題
const STORE_TITLES = {
    accountTypes: '帳戶類型',
    accountingItems: '會計項目代號表',
    accounts: '帳戶資料',
    advances: '代墊主表',
    advanceDetails: '代墊明細',
    advance_types: '代墊類型',
    banks: '銀行資料',
    departments: '部門資料',
    employees: '員工資料',
    entities: '交易對象資料',
    entity_subtypes: '交易對象細分類型',
    entity_types: '交易對象類型',//客戶、供應商、員工、其他
    paymentMethods: '支付方式',
    positions: '職位資料',
    projectBudgetCosts: '專案預算成本',
    projectContractPayments: '專案合約付款',
    projectTags: '專案標籤關聯',
    projects: '專案資料',
    projectTasks: '專案任務',
    salaries: '薪資發放',
    salaryDetails: '薪資明細',
    salaryTypes: '薪資類型',
    supplier_types: '供應商類型',
    taxTypesRates: '稅別資料',
    transactionCategories: '交易分類項',
    transactionCategoryItems: '分類項目表',
    transactionDetails: '交易明細表',
    transactions: '交易記錄',
    transactionTags: '交易標籤',
    assets: '資產資料', // 新增資產資料表標題
    assetDisposals: '資產處分資料', // 新增資產處分資料表標題
    journalEntries: '會計分錄', // 新增會計分錄資料表標題
    companySettings: '公司設定' // 新增公司設定資料表標題
};

// 定義資料表欄位，提供給settings.html使用，或其他頁面查詢時使用
// 定義資料表欄位
const STORE_FIELDS = {
    "accountTypes": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "帳戶類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "accountingItems": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "code",label: "會計項目代碼",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "name",label: "會計項目名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "category",label: "會計項目分類",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "type",label: "會計項目類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "accounts": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "帳戶名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "bankCode",label: "銀行代碼",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "branchCode",label: "分行代號",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountNumber",label: "帳號",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountTypeId",label: "帳戶類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "balance",label: "餘額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "advances": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "advanceNo",label: "代墊單號",type: "text",required: true,isIndex: true,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeId",label: "員工ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "typeId",label: "代墊類型ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "status",label: "狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "applyDate",label: "申請日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "approvedAt",label: "核准日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "advanceDetails": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "advanceId",label: "代墊ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "item",label: "項目",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "advance_types": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "代墊類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "banks": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "code",label: "銀行代碼",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "name",label: "銀行名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "departments": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "部門名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "employees": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "empNo",label: "員工編號",type: "text",required: true,isIndex: true,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "姓名",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "email",label: "電子郵件",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "phone",label: "電話",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "address",label: "地址",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 2,defaultDisplay: false},
        { key: "departmentId",label: "部門ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "positionId",label: "職位ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "idNumber",label: "身分證字號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "salary",label: "薪資",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "s",fieldWidth: 1,defaultDisplay: true},
        { key: "supervisorAllowance",label: "主管津貼",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "s",fieldWidth: 1,defaultDisplay: false},
        { key: "mealAllowance",label: "餐費津貼",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "s",fieldWidth: 1,defaultDisplay: false},
        { key: "companyLaborInsurance",label: "公司負擔勞保費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "c",fieldWidth: 1,defaultDisplay: false},
        { key: "companyHealthInsurance",label: "公司負擔健保費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "c",fieldWidth: 1,defaultDisplay: false},
        { key: "companyRetirementInsurance",label: "公司負擔勞退費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "c",fieldWidth: 1,defaultDisplay: false},
        { key: "employeeHealthInsurance",label: "員工負擔健保費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "e",fieldWidth: 1,defaultDisplay: false},
        { key: "employeeLaborInsurance",label: "員工負擔勞保費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "e",fieldWidth: 1,defaultDisplay: false},
        { key: "employeeRetirementInsurance",label: "員工負擔勞退費(自提繳)",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "e",fieldWidth: 1,defaultDisplay: false},
        { key: "startDate",label: "到職日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "resignationDate",label: "離職日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "status",label: "在職狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "createdAt",label: "建立時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "updatedAt",label: "更新時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false}
    ],

    "entities": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "code",label: "編號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "type",label: "對象類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "subtypeId",label: "細分類型",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taxId",label: "統一編號",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "contactPerson",label: "聯絡人",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "phone",label: "電話",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "email",label: "電子郵件",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "address",label: "地址",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "updatedAt",label: "更新時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "entity_subtypes": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "類型名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "parentType",label: "父類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "entity_types": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "類型名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "type",label: "類型代號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "類型描述",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "paymentMethods": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "支付方式",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "positions": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "職位名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projectBudgetCosts": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectId",label: "專案ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "costItem",label: "成本項目",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "budgetAmount",label: "預算金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "actualCost",label: "實際成本",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projectContractPayments": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectId",label: "專案ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "totalContractValue",label: "合約總價",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "numberOfInstallments",label: "分期期數",type: "number",required: true,isIndex: true,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "installmentAmount",label: "每期金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "installmentDueDate",label: "每期應付日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "installmentPaidDate",label: "每期實際付款日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "installmentStatus",label: "每期付款狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "contractDate",label: "簽訂日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "contractNumber",label: "合約編號",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projects": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectNumber",label: "專案編號",type: "text",required: true,isIndex: true,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "name",label: "專案名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "clientId",label: "客戶ID",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "status",label: "狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "startDate",label: "開始日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "expectedEndDate",label: "預計完成日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "專案描述",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "date",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "updatedAt",label: "更新時間",type: "date",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projectTags": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectId",label: "專案ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "tagId",label: "標籤ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projectTasks": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectId",label: "專案ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskName",label: "任務名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskDescription",label: "任務描述",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskStatus",label: "任務狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskStartDate",label: "任務開始日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskEndDate",label: "任務結束日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskProgress",label: "任務進度",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "salaries": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "employeeId",label: "員工編號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "basicSalary",label: "基本薪資",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "overtimePayment",label: "加班費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "leaveDeduction",label: "請假扣項",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "bonus",label: "獎金",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "supervisorAllowance",label: "主管津貼",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "mealAllowance",label: "餐費津貼",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "companyLaborInsurance",label: "公司負擔勞保費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "company",fieldWidth: 1,defaultDisplay: true},
        { key: "companyHealthInsurance",label: "公司負擔健保費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "company",fieldWidth: 1,defaultDisplay: true},
        { key: "companyRetirementInsurance",label: "公司負擔勞退費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "company",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeHealthInsurance",label: "員工負擔健保費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "employee",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeLaborInsurance",label: "員工負擔勞保費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "employee",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeRetirementInsurance",label: "員工負擔勞退費(自提繳)",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "employee",fieldWidth: 1,defaultDisplay: true},
        { key: "addItems",label: "薪資加項總額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "deductItems",label: "薪資扣項總額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "actualPayment",label: "實發薪資",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "companyBurden",label: "公司負擔總額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeBurden",label: "員工負擔總額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "salaryPeriodsStart",label: "薪資期間開始",type: "date",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "salaryPeriodsEnd",label: "薪資期間結束",type: "date",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "status",label: "薪資狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "paymentDate",label: "發放日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "salaryDetails": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "salaryId",label: "薪資ID",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "itemType",label: "項目類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "salaryTypes": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "薪資類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "supplier_types": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "供應商類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "taxTypesRates": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "稅別名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "rate",label: "稅率",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactionCategories": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "分類名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "type",label: "類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactionCategoryItems": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "categoryId",label: "分類ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountingCode",label: "會計項目代碼",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountingName",label: "會計項目名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactionDetails": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "transactionId",label: "交易ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "itemNo",label: "項目編號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "item",label: "項目名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "price",label: "單價",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "quantity",label: "數量",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "unit",label: "單位",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "total",label: "複價",type: "number",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactions": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "invoiceDate",label: "憑證/發票日期",type: "date",required: false,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "paymentDate",label: "收款/付款日期",type: "date",required: false,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "expectedPaymentDate",label: "預計收/付款日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "transactionType",label: "交易類型",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "paymentDescription",label: "交易描述(會計代號)",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "entityId",label: "交易對象ID",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "entityType",label: "交易對象類型",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taxTypeId",label: "稅別ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "invoiceNumber",label: "發票號碼",type: "text",required: false,isIndex: false,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taxAmount",label: "稅額",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "fee",label: "手續費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "notes",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "tags",label: "標籤",type: "array",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountId",label: "帳戶ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "paymentStatus",label: "帳款到帳情形",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "transactionStatus",label: "交易狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "date",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactionTags": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "標籤名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "date",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "assets": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "資產名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "category",label: "資產類別",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "purchaseDate",label: "購買日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "purchasePrice",label: "購買價格",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "currentValue",label: "現值",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "status",label: "狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "assetDisposals": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "assetId",label: "資產ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "disposalDate",label: "處分日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "disposalMethod",label: "處分方式",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "處分金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}    
    ],

    "journalEntries": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "transactionId",label: "交易ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "entryType",label: "分錄類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountCode",label: "會計科目代號",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountName",label: "會計科目名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "debitAmount",label: "借方金額",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "creditAmount",label: "貸方金額",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "entryDate",label: "分錄日期",type: "date",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "摘要",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false}
    ],

    "companySettings": [
        { key: "id", label: "ID", type: "text", required: true, isIndex: false, unique: true, isInputRequired: false, group: "none", fieldWidth: 1, defaultDisplay: false },
        { key: "companyName", label: "公司名稱", type: "text", required: true, isIndex: false, unique: false, isInputRequired: true, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "logo", label: "公司LOGO(Base64)", type: "text", required: false, isIndex: false, unique: false, isInputRequired: false, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "updatedAt", label: "更新時間", type: "text", required: false, isIndex: false, unique: false, isInputRequired: false, group: "none", fieldWidth: 1, defaultDisplay: true }
    ]
};


const STORE_relationTable = [
    {
        id: 1,
        sourceStoreName: 'tableA',
        sourceKeyPath: 'id',
        relatedStoreName: 'tableB',
        relatedForeignKeyPath: 'a_id',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    },
    {
        id: 2,
        sourceStoreName: 'tableB',
        sourceKeyPath: 'id',
        relatedStoreName: 'tableC',
        relatedForeignKeyPath: 'b_id',
        onDelete: 'RESTRICT',
        onUpdate: 'DO_NOTHING'
    },
    // 潛在的循環關聯 (tableC 關聯回 tableA - 不良設計，我們需要檢查出來)
    {
        id: 3,
        sourceStoreName: 'tableC',
        sourceKeyPath: 'id',
        relatedStoreName: 'tableA',
        relatedForeignKeyPath: 'c_related_a_id',
        onDelete: 'DO_NOTHING',
        onUpdate: 'DO_NOTHING'
    }
];
//----------------------------------------------------------------
//測試STORE_FIELDS.STORE_TITLES.STORE_CONFIG.STORE_NAMES各自缺少項目 store
//使用STORE_NAMES作為標準。
//-------------------------------------------------------------------
/*
//先測試STORE_FIELDS
let missingStores = [];
let dataFields = Object.keys(STORE_FIELDS);
for (const storeName in STORE_NAMES) {
    if (!dataFields.includes(storeName)) {
        missingStores.push(storeName);
    }

}
console.log(`STORE_FIELDS缺少的資料表：`, missingStores);

//測試STORE_TITLES
missingStores = [];
dataFields = Object.keys(STORE_TITLES);
for (const storeName in STORE_NAMES) {
    if (!dataFields.includes(storeName)) {
        missingStores.push(storeName);
    }

}
console.log(`STORE_TITLES缺少的資料表：`, missingStores);

//測試STORE_CONFIG
missingStores = [];
dataFields = Object.keys(STORE_CONFIG);
for (const storeName in STORE_NAMES) {
    if (!dataFields.includes(storeName)) {
        missingStores.push(storeName);
    }
}
console.log(`STORE_CONFIG缺少的資料表：`, missingStores);


//測試各自項目數量
console.log(`STORE_NAMES的項目數量(標準)：`, Object.keys(STORE_NAMES).length);
console.log(`STORE_FIELDS的項目數量：`, Object.keys(STORE_FIELDS).length);
console.log(`STORE_TITLES的項目數量：`, Object.keys(STORE_TITLES).length);
console.log(`STORE_CONFIG的項目數量：`, Object.keys(STORE_CONFIG).length);

//先測試STORE_NAMES，使用STORE_FIELDS作為標準。
missingStores = [];
dataFields = Object.keys(STORE_NAMES);
for (const storeName in STORE_FIELDS) {
    if (!dataFields.includes(storeName)) {
        missingStores.push(storeName);
    }
}
console.log(`STORE_NAMES缺少的資料表：`, missingStores);

const mergedArray = [...new Set([...Object.keys(STORE_NAMES), ...Object.keys(STORE_FIELDS), ...Object.keys(STORE_TITLES), ...Object.keys(STORE_CONFIG)])];
console.log(`合併後的資料表數量：`, mergedArray.length);
console.log(`合併後的資料表：`, mergedArray);
*/



//表格資料輸入時進行欄位檢核。
function checkDataFields(data, storeName, isCheckRequired = true, isCorrectData = false) {
    const storeFields = STORE_FIELDS[storeName];
    let usedNonDefinedFields = [];//Data中使用了未定義欄位
    let nonDefinedFields;//data中未提交的定義欄位
    let missingFields;//必填欄位未填寫
    let typeErrorFields;//欄位類型錯誤
    let errorMessage;//錯誤訊息
    let resultError;//回傳結果
    
    if (isCorrectData) {
        //console.log("處理前", data);
        data = correctData(data, storeName);
        //console.log("處理後", data);
    }
    

    const dataFields = Object.keys(data);
    if (isCheckRequired) {
        nonDefinedFields = storeFields.filter(field => !dataFields.includes(field.key));
        dataFields.forEach(field => {
            if (!storeFields.find(f => f.key === field)) {
                usedNonDefinedFields.push(field);
            }
        });
        missingFields = storeFields.filter(field => field.required && (!data[field.key] && data[field.key] !== 0));
    } else {
        nonDefinedFields = [];
        usedNonDefinedFields = [];
        missingFields = [];
    }


    typeErrorFields = storeFields.filter(field => {
        if (data[field.key] === null ||
            data[field.key] === undefined ||
            data[field.key] === ''
        ) {
            return false;
        }
        if (field.type === 'number') {
            //return isNaN(Number(data[field.key]));
            return typeof data[field.key] !== 'number';
        } else if (field.type === 'text') {
            return typeof data[field.key] !== 'string';
        } else if (field.type === 'date') {
            return isNaN(new Date(data[field.key]).getTime());
        }
    });

    if (missingFields.length > 0) {
        errorMessage = '必填欄位未填寫';
    } else if (typeErrorFields.length > 0) {
        errorMessage = '欄位類型錯誤';
    } else if (missingFields.length > 0 && typeErrorFields.length > 0) {
        errorMessage = '必填欄位未填寫或欄位類型錯誤';
    } else {
        errorMessage = '無錯誤';
    }

    resultError = {
        usedNonDefinedFields: usedNonDefinedFields,
        nonDefinedFields: nonDefinedFields,
        missingFields: missingFields,
        typeErrorFields: typeErrorFields,
        message: errorMessage
    }
    return resultError;
}

//校正資料
function correctData(data, storeName) {
    const storeFields = STORE_FIELDS[storeName];

    storeFields.forEach(field => {
        if (data[field.key] === null || data[field.key] === undefined ||
            (typeof data[field.key] === "number" && isNaN(data[field.key]))) {
            if (Array.isArray(data)) {
                data.push({
                    [field.key]: ''
                });
            } else if (typeof data === 'object') {
                data[field.key] = '';
            }
        }

        if (field.type === 'number') {
            data[field.key] = Number(data[field.key]);
        } else if (field.type === 'text') {
            data[field.key] = String(data[field.key]);
        } else if (field.type === 'date') {
            if (data[field.key] === '') {
                data[field.key] = '';
            } else {
                data[field.key] = new Date(data[field.key]).toISOString().split('T')[0];
            }
        }
    });
    return data;
}


// 初始化資料庫
const initDB = () => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(DB_NAME, DB_VERSION);

        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);

        request.onupgradeneeded = (event) => {
            const db = event.target.result;

            // 建立所有資料表
            Object.entries(STORE_NAMES).forEach(([key, storeName]) => {
                if (!db.objectStoreNames.contains(storeName)) {
                    const store = db.createObjectStore(storeName, STORE_CONFIG[storeName]);

                    // 為不同資料表建立適當的索引
                    console.log(storeName);
                    STORE_FIELDS[storeName].forEach(field => {
                        if (field.isIndex) {
                            store.createIndex(field.key, field.key, { unique: field.unique });
                        }
                    });
                    // console.log(`已建立資料表：${storeName}`);
                }else{
                    console.log(`資料表已存在：${storeName}`);
                    
                }

            });
        };
    });
}




//--------------------------------取得資料--------------------------------


// 取得所有資料
async function getAllData(storeName, indexName = null, sort = 'next') {
    // indexName 為索引名稱，sort 為排序方式，"next" 為升序，"prev" 為降序
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readonly');
        const store = transaction.objectStore(storeName);
        let request;
        if (indexName) {
            const index = store.index(indexName);
            request = index.openCursor(null, sort);
            const results = [];
            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    results.push(cursor.value);
                    cursor.continue();
                } else {
                    resolve(results);
                }
            };
        } else {
            request = store.getAll();
            request.onsuccess = () => resolve(request.result);
        }

        request.onerror = () => reject(request.error);
    });
}


// 根據 ID 獲取單筆資料
async function getDataById(storeName, id) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        //console.log(store);
        // 先獲取現有資料
        const getRequest = store.get(String(id));
        //console.log(getRequest);
        getRequest.onsuccess = () => {
            resolve(getRequest.result);
        };
        getRequest.onerror = () => {
            console.error('獲取資料失敗');
            reject(getRequest.error);
        };
    });
}
// 根據 ID 獲取單筆資料
async function getDataByDateRange(storeName, field, startDate, endDate) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const range = IDBKeyRange.bound(startDate, endDate);
        const index = store.index(field);
        const getRequest = index.getAll(range);
        //console.log(getRequest);
        getRequest.onsuccess = () => {
            resolve(getRequest.result);
        };
        getRequest.onerror = () => {
            console.error('獲取資料失敗');
            reject(getRequest.error);
        };
    });
}
// 根據 ID 獲取單筆資料
async function getDataByIndex(storeName, field, value) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const range = IDBKeyRange.only(value);
        const index = store.index(field);
        const getRequest = index.getAll(range);
        //console.log(getRequest);
        getRequest.onsuccess = () => {
            resolve(getRequest.result);
        };
        getRequest.onerror = () => {
            console.error('獲取資料失敗');
            reject(getRequest.error);
        };
    });
}

// 從資料庫獲取值
async function getDatabaseValue(table, id, field) {
    try {
        const db = await initDB();
        const transaction = db.transaction(table, 'readonly');
        const store = transaction.objectStore(table);

        return new Promise((resolve, reject) => {
            const request = store.get(String(id));

            request.onsuccess = () => {
                const item = request.result;
                if (item && item[field] !== undefined) {
                    resolve(item[field]);
                } else {
                    reject(new Error('找不到指定的數據'));
                }
            };

            request.onerror = () => {
                reject(new Error('資料庫查詢失敗'));
            };
        });
    } catch (error) {
        throw new Error(`資料庫錯誤: ${error.message}`);
    }
}

// 根據條件獲取資料庫值
async function getDatabaseValuesByCondition(table, conditionField, operator, value) {
    //////////////////////////////////////////////////////////需修改
    //////////////////////////////////////////////////////////需修改，執行過濾問題應該使用index及游標
    try {
        const db = await initDB();
        const transaction = db.transaction(table, 'readonly');
        const store = transaction.objectStore(table);

        return new Promise((resolve, reject) => {
            const request = store.getAll();

            request.onsuccess = () => {
                const allData = request.result;
                const filteredData = allData.filter(item => {
                    const itemValue = item[conditionField];
                    const compareValue = Number(value);

                    switch (operator) {
                        case '<':
                            return itemValue < compareValue;
                        case '<=':
                            return itemValue <= compareValue;
                        case '>':
                            return itemValue > compareValue;
                        case '>=':
                            return itemValue >= compareValue;
                        default:
                            return false;
                    }
                });

                resolve(filteredData);
            };

            request.onerror = () => {
                reject(new Error('資料庫查詢失敗'));
            };
        });
    } catch (error) {
        throw new Error(`資料庫錯誤: ${error.message}`);
    }
}

// 獲取實體類型標籤
function getEntityTypeLabel(type) {
    const typeLabels = {
        'account': '帳戶',
        'client': '客戶',
        'supplier': '供應商',
        'temporary': '臨時'
    };
    return typeLabels[type] || type;
}

// 取得部門資料
async function getDepartments() {
    return await getAllData(STORE_NAMES.departments);
}

// 取得職位資料
async function getPositions(departmentId = null) {
    const positions = await getAllData(STORE_NAMES.positions);
    if (departmentId) {
        return positions.filter(pos => pos.departmentId === departmentId);
    }
    return positions;
}


//--------------------------------更新資料--------------------------------

// 更新整筆資料
async function updateData(storeName, updatedData) {
    
    // 將更新資料的ID取出，以便獲取舊資料進行合併更新。
    const { id, ...updatedDataWithoutId } = updatedData;
    
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        // 先獲取現有資料
        const getRequest = store.get(String(id));
        getRequest.onerror = () => {
            console.error('獲取資料失敗');
            reject(getRequest.error);
        };
        getRequest.onsuccess = () => {
            const existingData = getRequest.result;
            if (!existingData) {
                reject(new Error('找不到指定的資料'));
                return;
            }

            // 合併現有資料和更新資料
            const updatedData = {
                ...existingData,
                ...updatedDataWithoutId
            };

            //更新資料前進行檢核
            const checkDataFieldsResult = checkDataFields(updatedData, storeName, true, false);
            if (checkDataFieldsResult.missingFields.length > 0 ||
                checkDataFieldsResult.typeErrorFields.length > 0 ||
                checkDataFieldsResult.usedNonDefinedFields.length > 0 ||
                checkDataFieldsResult.nonDefinedFields.length > 0) {
                console.log(checkDataFieldsResult);
                return null;
                //return checkDataFieldsResult;
            }
            // 更新資料
            const updateRequest = store.put(updatedData);

            updateRequest.onerror = () => {
                console.error('更新資料失敗');
                reject(updateRequest.error);
            };

            updateRequest.onsuccess = () => {
                resolve(updateRequest.result);
            };
        };
    });
}

//--------------------------------新增資料--------------------------------

// 新增資料
async function addData(storeName, data,isAutoId = true) {
    const db = await initDB();
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const IS_UUID = true;//開發時使用，來指定ID欄位是否使用UUID，或由IndexedDB資料庫自動產生。

    let savingData;

    // 移除 id 欄位，讓 IndexedDB 自動產生
    const { id, ...dataWithoutId } = data;

    if (IS_UUID) {
        // 為ID欄位生成UUID
        savingData = {
            id: self.crypto.randomUUID(),
            ...dataWithoutId
        }
    } else {
        savingData = dataWithoutId;
    }

    //檢查資料欄位是否符合資料庫欄位，不進行修正。(開發時使用)
    const checkDataFieldsResult = checkDataFields(savingData, storeName, true, false);
    //不檢查資料欄位是否符合資料庫欄位，進行修正。(發布後使用)
    //const checkDataFieldsResult = checkDataFields(savingData, storeName,false,true);

    if (checkDataFieldsResult.missingFields.length > 0 ||
        checkDataFieldsResult.typeErrorFields.length > 0 ||
        checkDataFieldsResult.usedNonDefinedFields.length > 0 ||
        checkDataFieldsResult.nonDefinedFields.length > 0) {
        console.log(checkDataFieldsResult);
        return null;
        //return checkDataFieldsResult;
    }


    if(!isAutoId){
        savingData.id = data.id;
    }

        //console.log(savingData);
        return new Promise((resolve, reject) => {
            const request = store.add(savingData);

        request.onsuccess = () => {

            resolve(request.result); // 解析 Promise 並回傳 ID
        };
        request.onerror = () => reject(request.error);
    });
}

//--------------------------------刪除資料--------------------------------

// 刪除資料
async function deleteData(storeName, id) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.delete(id);

        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
    });
}

//使用索引刪除資料，目前主要為關聯表使用(projectTags、projectTasks、projectBudgetsCosts、projectPayments)
async function deleteDataByIndex(storeName, indexName, value) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const index = store.index(indexName);
        const request = index.openCursor(IDBKeyRange.only(value));

        request.onsuccess = (event) => {
            const cursor = event.target.result;
            if (cursor) {
                cursor.delete();
                cursor.continue();
            } else {
                resolve();
            }
        };
        request.onerror = () => reject(request.error);
    });
}

//--------------------------------資料庫備份功能--------------------------------

// 備份資料庫
async function backupDatabase() {
    const db = await initDB();
    const data = {};

    // 取得所有資料表的資料
    for (const storeName of Object.values(STORE_NAMES)) {
        const store = db.transaction(storeName, 'readonly').objectStore(storeName);
        const request = store.getAll();

        data[storeName] = await new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // 建立下載檔案
    const date = new Date().toISOString().split('T')[0];
    const filename = `finance_data_${date}.json`;
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });

    return { blob, filename };
}

// 匯出資料
async function exportData() {
    const db = await initDB();
    const data = {};

    // 只匯出基礎資料相關的資料表
    const basicStores = [
        'banks',
        'accountTypes',
        'departments',
        'positions',
        'paymentMethods',
        'taxTypesRates',
    ];

    for (const storeName of basicStores) {
        const store = db.transaction(storeName, 'readonly').objectStore(storeName);
        const request = store.getAll();

        await new Promise((resolve, reject) => {
            request.onsuccess = () => {
                data[storeName] = request.result;
                resolve();
            };
            request.onerror = () => reject(request.error);
        });
    }

    return JSON.stringify(data, null, 2);
}

// 匯入資料
async function importData(data) {
    const db = await initDB();

    // 為每個資料表建立獨立的交易
    for (const [storeName, items] of Object.entries(data)) {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);

        // 清空現有資料
        await store.clear();

        // 新增匯入的資料
        for (const item of items) {
            await store.add(item);
        }

        // 等待交易完成
        await new Promise((resolve, reject) => {
            transaction.oncomplete = () => resolve();
            transaction.onerror = () => reject(transaction.error);
        });
    }
}


//--------------------------------代墊明細(新增、更新、刪除、查詢)--------------------------------
// 獲取代墊明細
async function getAdvanceDetails(advanceId) {
    const db = await initDB();
    const transaction = db.transaction(['advanceDetails'], 'readonly');
    const store = transaction.objectStore('advanceDetails');
    const index = store.index('advanceId');

    return new Promise((resolve, reject) => {
        const request = index.getAll(IDBKeyRange.only(advanceId));
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
}


// 新增代墊明細
async function addAdvanceDetails(details) {
    const db = await initDB();
    const transaction = db.transaction(['advanceDetails'], 'readwrite');
    const store = transaction.objectStore('advanceDetails');

    const addPromises = details.map(detail => {
        return new Promise((resolve, reject) => {
            const request = store.add(detail);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    });

    return Promise.all(addPromises);
}

// 更新代墊明細
async function updateAdvanceDetails(advanceId, details) {
    const db = await initDB();
    const transaction = db.transaction(['advanceDetails'], 'readwrite');
    const store = transaction.objectStore('advanceDetails');

    // 先刪除原有明細
    const index = store.index('advanceId');

    return new Promise(async (resolve, reject) => {
        const request = index.openCursor(IDBKeyRange.only(advanceId));

        request.onsuccess = async (event) => {
            const cursor = event.target.result;
            if (cursor) {
                cursor.delete();
                cursor.continue();
            } else {
                try {
                    // 刪除完成後，新增新的明細
                    await addAdvanceDetails(details.map(detail => ({
                        ...detail,
                        advanceId
                    })));
                    resolve();
                } catch (error) {
                    reject(error);
                }
            }
        };
        request.onerror = () => reject(request.error);
    });
}

// 刪除代墊明細
async function deleteAdvanceDetails(advanceId) {
    const db = await initDB();
    const transaction = db.transaction(['advanceDetails'], 'readwrite');
    const store = transaction.objectStore('advanceDetails');
    const index = store.index('advanceId');

    return new Promise((resolve, reject) => {
        const request = index.openCursor(IDBKeyRange.only(advanceId));
        request.onsuccess = (event) => {
            const cursor = event.target.result;
            if (cursor) {
                cursor.delete();
                cursor.continue();
            } else {
                resolve();
            }
        };
        request.onerror = () => reject(request.error);
    });
}



//--------------------------------薪資相關函數(新增、更新、刪除、查詢)--------------------------------
// 新增薪資相關函數
async function addSalary(salaryData) {
    const db = await initDB();
    const transaction = db.transaction(['salaries'], 'readwrite');
    const store = transaction.objectStore('salaries');

    return new Promise((resolve, reject) => {
        const request = store.add(salaryData);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
}

async function updateSalary(salaryData) {
    const db = await initDB();
    const transaction = db.transaction(['salaries'], 'readwrite');
    const store = transaction.objectStore('salaries');

    return new Promise((resolve, reject) => {
        const request = store.put(salaryData);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
}

async function deleteSalary(salaryId) {
    const db = await initDB();
    const transaction = db.transaction(['salaries', 'salaryDetails'], 'readwrite');
    const salaryStore = transaction.objectStore('salaries');
    const detailStore = transaction.objectStore('salaryDetails');

    return new Promise(async (resolve, reject) => {
        try {
            // 先刪除相關的明細記錄
            const index = detailStore.index('salaryId');
            const request = index.openCursor(IDBKeyRange.only(salaryId));

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    cursor.delete();
                    cursor.continue();
                } else {
                    // 明細刪除完成後，刪除主表記錄
                    const deleteRequest = salaryStore.delete(salaryId);
                    deleteRequest.onsuccess = () => resolve();
                    deleteRequest.onerror = () => reject(deleteRequest.error);
                }
            };
            request.onerror = () => reject(request.error);
        } catch (error) {
            reject(error);
        }
    });
}


//--------------------------------交易明細(新增、更新、刪除、查詢)--------------------------------

// 新增交易明細相關函數
async function addTransactionDetails(details) {
    const db = await initDB();
    const transaction = db.transaction(['transactionDetails'], 'readwrite');
    const store = transaction.objectStore('transactionDetails');

    const addPromises = details.map(detail => {
        return new Promise((resolve, reject) => {
            const request = store.add(detail);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    });

    return Promise.all(addPromises);
}

async function updateTransactionDetails(transactionId, details) {
    const db = await initDB();
    const transaction = db.transaction(['transactionDetails'], 'readwrite');
    const store = transaction.objectStore('transactionDetails');

    // 先刪除原有明細
    const index = store.index('transactionId');

    return new Promise(async (resolve, reject) => {
        const request = index.openCursor(IDBKeyRange.only(transactionId));

        request.onsuccess = async (event) => {
            const cursor = event.target.result;
            if (cursor) {
                cursor.delete();
                cursor.continue();
            } else {
                try {
                    // 刪除完成後，新增新的明細
                    await addTransactionDetails(details.map(detail => ({
                        ...detail,
                        transactionId
                    })));
                    resolve();
                } catch (error) {
                    reject(error);
                }
            }
        };
        request.onerror = () => reject(request.error);
    });
}

async function deleteTransactionDetails(transactionId) {
    const db = await initDB();
    const transaction = db.transaction(['transactionDetails'], 'readwrite');
    const store = transaction.objectStore('transactionDetails');
    const index = store.index('transactionId');

    return new Promise((resolve, reject) => {
        const request = index.openCursor(IDBKeyRange.only(transactionId));
        request.onsuccess = (event) => {
            const cursor = event.target.result;
            if (cursor) {
                cursor.delete();
                cursor.continue();
            } else {
                resolve();
            }
        };
        request.onerror = () => reject(request.error);
    });
}

async function getTransactionDetails(transactionId) {
    const db = await initDB();
    const transaction = db.transaction(['transactionDetails'], 'readonly');
    const store = transaction.objectStore('transactionDetails');
    const index = store.index('transactionId');

    return new Promise((resolve, reject) => {
        const request = index.getAll(IDBKeyRange.only(transactionId));
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
}

//--------------------------------專案標籤(新增、更新、刪除、查詢)--------------------------------


// 更新專案標籤
async function updateProjectTags(projectID, newTags) {
    const db = await initDB();
    const transaction = db.transaction(['projectTags'], 'readwrite');
    const store = transaction.objectStore('projectTags');
    // 1. 刪除現有標籤
    // 假設你已經為 projectID 創建了一個索引，名稱為 projectId
    const index = store.index('projectId');
    const deleteRequests = [];
    return new Promise((resolve, reject) => {
        index.openCursor(IDBKeyRange.only(projectID)).onsuccess = (cursorEvent) => {
            const cursor = cursorEvent.target.result;
            if (cursor) {
                deleteRequests.push(cursor.delete()); // 刪除當前記錄
                cursor.continue();
            } else {
                // 游標遍歷完成，等待所有刪除請求完成
                Promise.all(deleteRequests)
                    .then(() => {
                        // 2. 新增新的標籤
                        const addRequests = [];
                        newTags.forEach(tag => {
                            addRequests.push(
                                store.add({
                                    projectId: projectID,
                                    tagId: tag
                                })
                            );
                        });
                        return Promise.all(addRequests);
                    })
                    .then(() => {
                        // 事務完成
                        transaction.oncomplete = () => {
                            console.log(`專案 ${projectID} 的標籤已更新。`);
                            resolve();
                        };
                        transaction.onerror = (txError) => {
                            console.error('事務錯誤:', txError.target.errorCode);
                            reject(txError.target.errorCode);
                        };
                        transaction.onabort = () => {
                            console.warn('事務已中止');
                            reject('Transaction aborted');
                        };
                    })
                    .catch(addError => {
                        console.error('新增標籤失敗:', addError);
                        transaction.abort(); // 出錯時中止事務
                        reject(addError);
                    });
            }
        };
        index.openCursor(IDBKeyRange.only(projectID)).onerror = (error) => {
            console.error('開啟游標錯誤:', error);
            reject(error);
        };
    });
}

// 呼叫範例
// updateProjectTags(123, ['UI設計', '前端開發', '響應式'])
//   .then(() => console.log('標籤更新成功'))
//   .catch(err => console.error('標籤更新失敗:', err));

// 注意：你需要確保 'byProjectID' 索引已經存在。如果沒有，需要在 onupgradeneeded 中創建。
// db.createObjectStore('projectTags', { keyPath: ['projectID', 'tag'] }); // 如果 projectID 和 tag 是複合主鍵
// 或者
// store.createIndex('byProjectID', 'projectID', { unique: false }); // 如果 projectID 不是唯一鍵，但你需要查詢所有相關標籤




//--------------------------------資料表讀取及過濾功能--------------------------------

//----尚未實作----尚未實作----尚未實作----尚未實作----尚未實作
async function getStoreFields(storeName) {
    return STORE_FIELDS[storeName];
}

async function getStoreData(storeName, getFields = null, filter = null) {

}
//----尚未實作----尚未實作----尚未實作----尚未實作----尚未實作----尚未實作


//-------------------------------------------------------------------------------------




// 確保函數可以在瀏覽器和Node.js環境中使用
if (typeof window !== 'undefined') {
    window.initDB = initDB;
}
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initDB };
}

// 資產管理相關函數
async function saveAsset(assetData) {
    try {
        const db = await initDB();
        const tx = db.transaction(STORE_NAMES.assets, 'readwrite');
        const store = tx.objectStore(STORE_NAMES.assets);
        
        // 確保有ID
        if (!assetData.id) {
            assetData.id = self.crypto.randomUUID();
        }
        
        await store.put(assetData);
        await tx.complete;
        return true;
    } catch (error) {
        console.error('儲存資產失敗:', error);
        throw error;
    }
}

async function updateAsset(assetData) {
    try {
        const db = await initDB();
        const tx = db.transaction(STORE_NAMES.assets, 'readwrite');
        const store = tx.objectStore(STORE_NAMES.assets);
        
        if (!assetData.id) {
            throw new Error('更新資產時需要ID');
        }
        
        assetData.updatedAt = new Date().toISOString();
        await store.put(assetData);
        await tx.complete;
        return true;
    } catch (error) {
        console.error('更新資產失敗:', error);
        throw error;
    }
}

async function deleteAsset(id) {
    try {
        const db = await initDB();
        const tx = db.transaction(STORE_NAMES.assets, 'readwrite');
        const store = tx.objectStore(STORE_NAMES.assets);
        
        await store.delete(id);
        await tx.complete;
        return true;
    } catch (error) {
        console.error('刪除資產失敗:', error);
        throw error;
    }
}

async function getAssetById(id) {
    try {
        const db =await initDB();
        const tx = db.transaction(STORE_NAMES.assets, 'readonly');
        const store = tx.objectStore(STORE_NAMES.assets);
        
        const asset = await store.get(id);
        return asset;
    } catch (error) {
        console.error('獲取資產失敗:', error);
        throw error;
    }
}

async function saveAssetDisposal(disposalData) {
    try {
        const db = await initDB();
        const tx = db.transaction(STORE_NAMES.assetDisposals, 'readwrite');
        const store = tx.objectStore(STORE_NAMES.assetDisposals);
        
        if (!disposalData.id) {
            disposalData.id = self.crypto.randomUUID();
        }
        
        await store.put(disposalData);
        await tx.complete;
        return true;
    } catch (error) {
        console.error('儲存資產處分記錄失敗:', error);
        throw error;
    }
}

// 更新資產折舊
async function updateAssetDepreciation(assetId, depreciationAmount) {
    try {
        const asset = await getAssetById(assetId);
        if (!asset) {
            throw new Error('找不到資產');
        }

        asset.accumulatedDepreciation = (asset.accumulatedDepreciation || 0) + depreciationAmount;
        asset.updatedAt = new Date().toISOString();

        await updateAsset(asset);
        return true;
    } catch (error) {
        console.error('更新資產折舊失敗:', error);
        throw error;
    }
}

async function saveAssetDepreciation(assetId, depreciationData) {
    const collection = await getCollection('assetDepreciation');
    return await collection.updateOne(
        { assetId },
        { 
            $set: {
                method: depreciationData.method,
                schedule: depreciationData.schedule,
                totalProductionUnits: depreciationData.totalProductionUnits,
                lastUpdated: new Date()
            }
        },
        { upsert: true }
    );
}

async function getAssetDepreciation(assetId) {
    const collection = await getCollection('assetDepreciation');
    return await collection.findOne({ assetId });
}


//--------------------------------會計分錄(新增、更新、刪除、查詢)--------------------------------

// 儲存會計分錄
async function saveJournalEntries(transactionId, journalData) {
    try {
        const db = await initDB();
        const transaction = db.transaction(STORE_NAMES.journalEntries, 'readwrite');
        const store = transaction.objectStore(STORE_NAMES.journalEntries);

        const entries = [];
        const currentTime = new Date().toISOString();

        // 處理借方分錄
        if (journalData.debit && journalData.debit.length > 0) {
            journalData.debit.forEach(debitEntry => {
                entries.push({
                    id: self.crypto.randomUUID(),
                    transactionId: transactionId,
                    entryType: 'debit',
                    accountCode: Number(debitEntry.code),
                    accountName: debitEntry.name,
                    debitAmount: debitEntry.amount,
                    creditAmount: 0,
                    entryDate: debitEntry.date,
                    description: `${journalData.debitEntity || ''} - ${debitEntry.name}`,
                    createdAt: currentTime
                });
            });
        }

        // 處理貸方分錄
        if (journalData.credit && journalData.credit.length > 0) {
            journalData.credit.forEach(creditEntry => {
                entries.push({
                    id: self.crypto.randomUUID(),
                    transactionId: transactionId,
                    entryType: 'credit',
                    accountCode: creditEntry.code,
                    accountName: creditEntry.name,
                    debitAmount: 0,
                    creditAmount: creditEntry.amount,
                    entryDate: creditEntry.date,
                    description: `${journalData.creditEntity || ''} - ${creditEntry.name}`,
                    createdAt: currentTime
                });
            });
        }

        // 批量儲存分錄
        const savePromises = entries.map(entry => {
            return new Promise((resolve, reject) => {
                const request = store.add(entry);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        });

        await Promise.all(savePromises);
        return entries.length;
    } catch (error) {
        console.error('儲存會計分錄失敗:', error);
        throw error;
    }
}

// 根據交易ID獲取會計分錄
async function getJournalEntriesByTransactionId(transactionId) {
    try {
        const db = await initDB();
        const transaction = db.transaction(STORE_NAMES.journalEntries, 'readonly');
        const store = transaction.objectStore(STORE_NAMES.journalEntries);
        const index = store.index('transactionId');

        return new Promise((resolve, reject) => {
            const request = index.getAll(IDBKeyRange.only(transactionId));
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    } catch (error) {
        console.error('獲取會計分錄失敗:', error);
        throw error;
    }
}

// 刪除交易相關的會計分錄
async function deleteJournalEntriesByTransactionId(transactionId) {
    try {
        const db = await initDB();
        const transaction = db.transaction(STORE_NAMES.journalEntries, 'readwrite');
        const store = transaction.objectStore(STORE_NAMES.journalEntries);
        const index = store.index('transactionId');

        return new Promise((resolve, reject) => {
            const request = index.openCursor(IDBKeyRange.only(transactionId));
            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    cursor.delete();
                    cursor.continue();
                } else {
                    resolve();
                }
            };
            request.onerror = () => reject(request.error);
        });
    } catch (error) {
        console.error('刪除會計分錄失敗:', error);
        throw error;
    }
}
